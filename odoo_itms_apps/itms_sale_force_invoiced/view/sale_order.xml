<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_order_form" model="ir.ui.view">
        <field name="name">sale.order.form</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form" />
        <field name="arch" type="xml">
            <group name="sale_info" position="inside">
                <field
                    name="force_invoiced"
                    groups="itms_sale_force_invoiced.group_force_invoiced"
                    widget="boolean_toggle"
                    invisible="state != 'sale'"
                    readonly="state != 'sale'"
                />
            </group>
        </field>
    </record>
</odoo>
