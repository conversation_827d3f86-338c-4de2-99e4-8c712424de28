<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="product_template_form_view" model="ir.ui.view">
        <field name="name">product.template.form.inherited</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view" />
        <field name="arch" type="xml">
            <field name="list_price" position="after">
                <div
                    name="fix_price_tooltip"
                    invisible="product_variant_count == 1 or is_product_variant"
                >
                    <span
                        class="text-muted fst-italic"
                    >Setting the price here will update all variants.</span>
                </div>
            </field>
        </field>
    </record>
    <record id="product_variant_easy_edit_price_view" model="ir.ui.view">
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_variant_easy_edit_view" />
        <field name="arch" type="xml">
            <!-- Make the field editable -->
            <field name="lst_price" position="attributes">
                <attribute name="readonly" />
            </field>
        </field>
    </record>
    <!-- Hide price extra fields, as they are no longer useful -->
    <record id="product_template_attribute_value_view_tree" model="ir.ui.view">
        <field name="name">product.template.attribute.value.tree.hide.extra</field>
        <field name="model">product.template.attribute.value</field>
        <field
            name="inherit_id"
            ref="product.product_template_attribute_value_view_tree"
        />
        <field name="arch" type="xml">
            <field name="price_extra" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
        </field>
    </record>
    <record id="product_template_attribute_value_view_form" model="ir.ui.view">
        <field name="name">product.template.attribute.value.form.hide.extra</field>
        <field name="model">product.template.attribute.value</field>
        <field
            name="inherit_id"
            ref="product.product_template_attribute_value_view_form"
        />
        <field name="arch" type="xml">
            <field name="price_extra" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
        </field>
    </record>
</odoo>
