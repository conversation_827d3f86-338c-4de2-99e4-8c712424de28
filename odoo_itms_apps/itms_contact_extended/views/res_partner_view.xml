<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_partner_form_inherit_is_customer_vendor" model="ir.ui.view">
        <field name="name">res.partner.view.form.inherit.is_customer_vendor</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('oe_title')]//h1" position="after">
                <group>
                    <group>
                        <group>
                            <field name="customer"/>
                        </group>
                        <group>
                            <field name="supplier"/>
                        </group>
                    </group>
                </group>
            </xpath>
            <xpath expr="//group[@name='purchase']" position="attributes">
                <attribute name="invisible">supplier == False</attribute>
            </xpath>
            <xpath expr="//group[@name='sale']" position="attributes">
                <attribute name="invisible">customer == False</attribute>
            </xpath>
            <xpath expr="//span[@name='address_name']" position="before">
                    <!-- <field name="customer_type" invisible="1"/>
                    <field name="customer_type_id" domain="[('ttype', '=', 'customer')]" options="{'no_create': True, 'no_open': True}" invisible="customer != True or supplier == True"/>
                    <field name="customer_type_id" domain="[('ttype', '=', 'supplier')]" options="{'no_create': True, 'no_open': True}" invisible="supplier != True or customer == True"/>
                    <field name="customer_type_id" invisible="supplier == False or customer == False" domain="[('ttype', '=', 'both')]" options="{'no_create': True, 'no_open': True}"/> -->
            </xpath>
        </field>
    </record>

    <record id="view_partner_property_form_inherit" model="ir.ui.view">
        <field name="name">view.partner.property.form.inheritr</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="account.view_partner_property_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='bank_id']" position="after">
                <field name="aba_bsb"/>
            </xpath>
        </field>
    </record>

    <record id="view_order_form_inherit__is_customer_vendor" model="ir.ui.view">
        <field name="name">sale.order.view.form.inherit.is_customer_vendor</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <field name="partner_id" position="attributes">
                <attribute name="context">
                    {'res_partner_search_mode': 'customer', 'show_address': 1, 'show_vat': True,'default_customer':True,'default_is_company':True,}
                </attribute>
                <attribute name="domain">[('customer','=',True)]</attribute>
            </field>
        </field>
    </record>

    <record id="purchase_order_form_inherit_is_customer_vendor" model="ir.ui.view">
        <field name="name">purchase.order.view.form.inherit.is_customer_vendor</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            <field name="partner_id" position="attributes">
                <attribute name="context">
                    {'res_partner_search_mode': 'supplier', 'show_vat':True, 'default_supplier':True, 'default_is_company':True}
                </attribute>
                <attribute name="domain">[('supplier','=',True)]</attribute>
            </field>
        </field>
    </record>

</odoo>
