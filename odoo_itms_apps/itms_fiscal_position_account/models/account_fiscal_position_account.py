from odoo import fields, models


class AccountFiscalPositionAccount(models.Model):
    _inherit = "account.fiscal.position.account"

    product_category_id = fields.Many2one(
        "product.category", string="Product Category", required=True
    )

    _sql_constraints = [
        (
            "account_src_dest_uniq",
            "unique (position_id,account_src_id,product_category_id,account_dest_id)",
            "An account fiscal position could be defined only one time on same accounts/category.",
        )
    ]


class AccountFiscalPosition(models.Model):
    _inherit = "account.fiscal.position"

    def map_accounts(self, accounts):
        """Receive a dictionary having accounts in values and try to
        replace those accounts accordingly to the fiscal position.
        """
        ref_dict = {}
        for line in self.account_ids:
            ref_dict[
                line.account_src_id.name + line.product_category_id.name
            ] = line.account_dest_id
        category = ""
        if "category" in accounts.keys():
            category = accounts["category"].name
        for key, acc in accounts.items():
            if acc:
                account_name = acc.name
            if account_name + category in ref_dict:
                accounts[key] = ref_dict[account_name + category]
        return accounts


class ProductTemplate(models.Model):
    _inherit = "product.template"

    def _get_product_accounts(self):
        res = super()._get_product_accounts()
        res.update({"category": self.categ_id})
        return res
