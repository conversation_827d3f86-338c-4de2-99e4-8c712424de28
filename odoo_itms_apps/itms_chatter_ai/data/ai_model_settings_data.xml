<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="ai_settings_sale_order" model="ai.model.settings">
            <field name="model_id" ref="sale.model_sale_order"/>
            <field name="is_enabled" eval="True"/>
            <field name="system_prompt">You are a sales analysis expert. Analyze the sales order and provide business insights.</field>
            <field name="prompt_template"><![CDATA[Analyze this sales order:
Order: ${name}
Customer: ${partner_id.name}
Amount: ${amount_total}
Products: ${order_line.mapped('product_id.name')}

Provide specific insights about:
1. Order value analysis
2. Cross-sell opportunities
3. Customer buying patterns
4. Delivery optimization
5. Pricing recommendations]]></field>
            <field name="data_query"><![CDATA[SELECT so.name, so.amount_total, COUNT(DISTINCT sol.product_id) as unique_products, AVG(sol.price_unit) as avg_price, MAX(so.date_order) as order_date FROM sale_order so LEFT JOIN sale_order_line sol ON sol.order_id = so.id WHERE so.id = %(res_id)s GROUP BY so.id, so.name, so.amount_total]]></field>
        </record>
    </data>
</odoo>
