<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- General Opportunity Analysis Template -->
        <record id="crm_prompt_template_general" model="ai.prompt.template">
            <field name="name">Opportunity Analysis</field>
            <field name="template_key">crm_general</field>
            <field name="description">Overall analysis of the opportunity</field>
            <field name="model_id" ref="crm.model_crm_lead"/>
            <field name="sequence">10</field>
            <field name="system_prompt"><![CDATA[You are an expert sales consultant with deep expertise in analyzing CRM opportunities. You provide strategic and tactical advice to sales teams to help them close more deals effectively.]]></field>
            <field name="prompt_template"><![CDATA[Analyze this sales opportunity and provide insights to help the sales team close the deal:

OPPORTUNITY DETAILS:
- Name: ${name}
- Customer: ${partner_name}
- Expected Revenue: ${expected_revenue}
- Probability: ${probability}%
- Stage: ${stage_name}
- Type: ${type}

Provide a comprehensive analysis including:
1. Overall assessment of this opportunity's health and likelihood to close
2. Key strengths and potential weaknesses in the current sales approach
3. Specific recommendations for moving this opportunity forward
4. Risk factors to be aware of
5. Suggested timeline and action items

Be specific and actionable in your analysis, taking into account the current stage and probability.]]></field>
            <field name="is_enabled" eval="True"/>
        </record>

        <!-- Win Strategy Template -->
        <record id="crm_prompt_template_win_strategy" model="ai.prompt.template">
            <field name="name">Win Strategy</field>
            <field name="template_key">crm_win_strategy</field>
            <field name="description">Detailed strategy to win this opportunity</field>
            <field name="model_id" ref="crm.model_crm_lead"/>
            <field name="sequence">20</field>
            <field name="system_prompt"><![CDATA[You are a strategic sales consultant who specializes in developing winning strategies for complex sales opportunities. You help sales teams identify the best approaches to secure contracts and overcome obstacles.]]></field>
            <field name="prompt_template"><![CDATA[Create a detailed win strategy for this sales opportunity:

OPPORTUNITY DETAILS:
- Name: ${name}
- Customer: ${partner_name}
- Expected Revenue: ${expected_revenue}
- Probability: ${probability}%
- Stage: ${stage_name}
- Type: ${type}

Provide a comprehensive win strategy including:
1. Value proposition: How to position our solution to match the customer's specific needs
2. Competitive advantages: How to differentiate from competitors
3. Decision makers: Key stakeholders to influence and how to approach them
4. Potential objections: Anticipated concerns and how to address them
5. Timeline: Critical milestones to advance the opportunity
6. Resources needed: What the sales team needs to succeed

Be specific and actionable in your strategy, taking into account the current stage and probability.]]></field>
            <field name="is_enabled" eval="True"/>
        </record>

        <!-- Sales Coaching Template -->
        <record id="crm_prompt_template_sales_coaching" model="ai.prompt.template">
            <field name="name">Sales Coaching</field>
            <field name="template_key">crm_sales_coaching</field>
            <field name="description">Coaching advice for sales representatives</field>
            <field name="model_id" ref="crm.model_crm_lead"/>
            <field name="sequence">30</field>
            <field name="system_prompt"><![CDATA[You are an experienced sales coach who helps sales professionals improve their skills and close more deals. You provide practical advice that can be immediately applied to specific sales opportunities.]]></field>
            <field name="prompt_template"><![CDATA[Provide sales coaching advice for this opportunity:

OPPORTUNITY DETAILS:
- Name: ${name}
- Customer: ${partner_name}
- Expected Revenue: ${expected_revenue}
- Probability: ${probability}%
- Stage: ${stage_name}
- Type: ${type}

Offer coaching insights in the following areas:
1. Discovery questions: What critical information might still be missing
2. Value alignment: How to better connect our solution to the customer's business needs
3. Objection handling: How to address common concerns at this stage
4. Advancing techniques: Specific actions to move the deal forward
5. Closing strategies: Approaches to improve likelihood of winning the deal

Frame your advice as if you're coaching the sales representative directly, with practical tips they can use immediately.]]></field>
            <field name="is_enabled" eval="True"/>
        </record>

        <!-- Customer Needs Assessment Template -->
        <record id="crm_prompt_template_needs_assessment" model="ai.prompt.template">
            <field name="name">Customer Needs Assessment</field>
            <field name="template_key">crm_needs_assessment</field>
            <field name="description">Analysis of customer needs based on opportunity data</field>
            <field name="model_id" ref="crm.model_crm_lead"/>
            <field name="sequence">40</field>
            <field name="system_prompt"><![CDATA[You are a customer intelligence expert who helps sales teams understand the deeper needs and motivations of their prospects. You translate opportunity data into actionable insights about what the customer truly values.]]></field>
            <field name="prompt_template"><![CDATA[Analyze the potential needs and pain points of this customer based on the opportunity information:

OPPORTUNITY DETAILS:
- Name: ${name}
- Customer: ${partner_name}
- Expected Revenue: ${expected_revenue}
- Probability: ${probability}%
- Stage: ${stage_name}
- Type: ${type}

Provide insights on the following:
1. Likely business challenges this customer is facing
2. Potential goals and objectives they're trying to achieve
3. Key decision criteria they may be using to evaluate solutions
4. Implicit needs that may not be explicitly stated
5. Recommended approach to further uncover and validate these needs
6. How our solution could address these specific needs

Use industry knowledge and the context provided to make educated inferences about what matters most to this customer.]]></field>
            <field name="is_enabled" eval="True"/>
        </record>

        <!-- Follow-up Activities Template -->
        <record id="crm_prompt_template_follow_up" model="ai.prompt.template">
            <field name="name">Follow-up Activities</field>
            <field name="template_key">crm_follow_up</field>
            <field name="description">Recommended follow-up activities for this opportunity</field>
            <field name="model_id" ref="crm.model_crm_lead"/>
            <field name="sequence">50</field>
            <field name="system_prompt"><![CDATA[You are a sales operations expert who helps sales teams maintain momentum in their pipeline through effective follow-up activities. You provide tactical recommendations for what to do next to advance opportunities.]]></field>
            <field name="prompt_template"><![CDATA[Recommend specific follow-up activities for this opportunity:

OPPORTUNITY DETAILS:
- Name: ${name}
- Customer: ${partner_name}
- Expected Revenue: ${expected_revenue}
- Probability: ${probability}%
- Stage: ${stage_name}
- Type: ${type}
- Activity Count: ${activity_count}
- Next Activity Date: ${next_activity_date}

Suggest 5 highly relevant follow-up activities including:
1. Immediate next actions (next 48 hours)
2. Short-term activities (next 1-2 weeks)
3. Key meetings or presentations to schedule
4. Stakeholders to engage with
5. Content or materials to prepare and share

For each activity, include:
- A clear action description
- The purpose/objective of the activity
- Suggested timing
- Expected outcome

Make the activities specific to the current stage of the opportunity and designed to move it to the next stage in the sales process.]]></field>
            <field name="is_enabled" eval="True"/>
        </record>

        <!-- Deal Risk Assessment Template -->
        <record id="crm_prompt_template_risk_assessment" model="ai.prompt.template">
            <field name="name">Deal Risk Assessment</field>
            <field name="template_key">crm_risk_assessment</field>
            <field name="description">Analysis of risks and mitigation strategies</field>
            <field name="model_id" ref="crm.model_crm_lead"/>
            <field name="sequence">60</field>
            <field name="system_prompt"><![CDATA[You are a risk analysis expert who helps sales teams identify and mitigate potential issues that could derail sales opportunities. You provide balanced assessments that highlight both risks and potential solutions.]]></field>
            <field name="prompt_template"><![CDATA[Provide a risk assessment for this opportunity:

OPPORTUNITY DETAILS:
- Name: ${name}
- Customer: ${partner_name}
- Expected Revenue: ${expected_revenue}
- Probability: ${probability}%
- Stage: ${stage_name}
- Type: ${type}

Conduct a comprehensive risk assessment including:
1. Top 3-5 risk factors that could prevent this deal from closing
2. Early warning signs to watch for with each risk
3. Specific mitigation strategies for each identified risk
4. Opportunity timeline risks (delays, budget cycles, approval processes)
5. Competitive threats and how to counter them
6. Internal risks (resource availability, delivery capabilities, etc.)

For each risk, provide a severity assessment (High/Medium/Low) and specific actions that could reduce or eliminate the risk.]]></field>
            <field name="is_enabled" eval="True"/>
        </record>
    </data>
</odoo>
