<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="ai_settings_crm_lead" model="ai.model.settings">
            <field name="model_id" ref="crm.model_crm_lead"/>
            <field name="is_enabled" eval="True"/>
            <field name="system_prompt">You are a CRM and sales opportunity analysis expert. Analyze the opportunity and provide strategic insights.</field>
            <field name="prompt_template"><![CDATA[Analyze this opportunity:
Name: ${name}
Expected Revenue: ${expected_revenue}
Probability: ${probability}
Stage: ${stage_name}
Create Date: ${create_date}
Type: ${type}

Provide specific insights about:
1. Current opportunity status
2. Win probability assessment
3. Next action recommendations
4. Potential roadblocks
5. Strategy suggestions]]></field>
            <field name="data_query"><![CDATA[SELECT
    cl.name,
    cl.expected_revenue,
    cl.probability,
    cl.type,
    cl.create_date,
    cs.name as stage_name,
    rp.name as partner_name,
    (SELECT COUNT(*) FROM mail_activity ma WHERE ma.res_id = cl.id AND ma.res_model = 'crm.lead') as activity_count,
    (SELECT MAX(ma.date_deadline) FROM mail_activity ma WHERE ma.res_id = cl.id AND ma.res_model = 'crm.lead') as next_activity_date
FROM
    crm_lead cl
LEFT JOIN
    crm_stage cs ON cl.stage_id = cs.id
LEFT JOIN
    res_partner rp ON cl.partner_id = rp.id
WHERE
    cl.id = %(res_id)s]]></field>
        </record>
    </data>
</odoo>
