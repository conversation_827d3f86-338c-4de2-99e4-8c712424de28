<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Contact (res.partner) Templates -->

        <!-- General Analysis Template -->
        <record id="prompt_template_partner_general" model="ai.prompt.template">
            <field name="name">General Analysis</field>
            <field name="description">Overall analysis of the contact</field>
            <field name="template_key">general</field>
            <field name="sequence">10</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="is_default">True</field>
            <field name="is_enabled">True</field>
            <field name="prompt_template">Please analyze this contact information and provide insights:

Name: ${name}
Email: ${email}
Phone: ${phone}
Created: ${create_date}

Order Count: ${order_count}
Last Order Date: ${last_order_date}

Provide a summary of this contact, including:
1. Contact status and engagement level
2. Recommendations for follow-up actions
3. Any potential opportunities based on their history</field>
            <field name="system_prompt">You are an expert business analyst helping to analyze customer data. Provide concise, actionable insights based on the available information. If data is limited, acknowledge this but still provide the best possible analysis.</field>
        </record>

        <!-- Follow Up Template -->
        <record id="prompt_template_partner_follow_up" model="ai.prompt.template">
            <field name="name">Follow Up</field>
            <field name="description">Recommendations for follow-up actions</field>
            <field name="template_key">follow_up</field>
            <field name="sequence">20</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="is_default">False</field>
            <field name="is_enabled">True</field>
            <field name="prompt_template">Please analyze this contact and provide follow-up recommendations:

Name: ${name}
Email: ${email}
Phone: ${phone}
Created: ${create_date}

Order Count: ${order_count}
Last Order Date: ${last_order_date}

Provide specific follow-up actions for this contact, including:
1. Recommended timing for the next contact
2. Specific talking points based on their history
3. Potential products or services to discuss
4. Communication channel recommendations (email, phone, in-person)
5. Any special considerations or personalization opportunities</field>
            <field name="system_prompt">You are a sales coach helping to plan follow-up activities with customers. Provide specific, actionable recommendations for effective follow-up. Focus on timing, approach, and personalization.</field>
        </record>

        <!-- Purchase History Template -->
        <record id="prompt_template_partner_purchase_history" model="ai.prompt.template">
            <field name="name">Purchase History</field>
            <field name="description">Analysis of purchase patterns and history</field>
            <field name="template_key">purchase_history</field>
            <field name="sequence">30</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="is_default">False</field>
            <field name="is_enabled">True</field>
            <field name="prompt_template">Please analyze this contact's purchase history:

Name: ${name}
Email: ${email}
Phone: ${phone}

Order Count: ${order_count}
Last Order Date: ${last_order_date}
Total Annual Spend: ${total_annual_spend}
Total Monthly Spend: ${total_monthly_spend}
Average Order Value: ${average_order_value}

Provide a detailed analysis of their purchasing patterns, including:
1. Spending trends (increasing, decreasing, seasonal)
2. Product category preferences
3. Purchase frequency patterns
4. Comparison to average customer metrics
5. Recommendations for increasing order value or frequency</field>
            <field name="system_prompt">You are a purchasing analyst specializing in customer buying patterns. Analyze the available data to identify trends, preferences, and opportunities to increase sales. Be specific and data-driven in your analysis.</field>
        </record>

        <!-- Customer Profile Template -->
        <record id="prompt_template_partner_customer_profile" model="ai.prompt.template">
            <field name="name">Customer Profile</field>
            <field name="description">Detailed customer profile and segmentation</field>
            <field name="template_key">customer_profile</field>
            <field name="sequence">40</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="is_default">False</field>
            <field name="is_enabled">True</field>
            <field name="prompt_template">Please create a detailed customer profile for this contact:

Name: ${name}
Email: ${email}
Phone: ${phone}
Created: ${create_date}

Order Count: ${order_count}
Last Order Date: ${last_order_date}
Total Annual Spend: ${total_annual_spend}

Create a comprehensive customer profile, including:
1. Customer segment categorization (high-value, mid-tier, occasional, etc.)
2. Estimated lifetime value projection
3. Customer persona characteristics
4. Likely needs and pain points
5. Communication preferences and style
6. Decision-making factors that may influence purchases</field>
            <field name="system_prompt">You are a customer profiling expert who specializes in creating detailed customer personas. Based on the available data, create a comprehensive profile that helps understand this customer better. Make reasonable inferences where data is limited.</field>
        </record>

        <!-- Engagement Strategy Template -->
        <record id="prompt_template_partner_engagement" model="ai.prompt.template">
            <field name="name">Engagement Strategy</field>
            <field name="description">Strategies to increase customer engagement</field>
            <field name="template_key">engagement</field>
            <field name="sequence">50</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="is_default">False</field>
            <field name="is_enabled">True</field>
            <field name="prompt_template">Please develop an engagement strategy for this contact:

Name: ${name}
Email: ${email}
Phone: ${phone}
Created: ${create_date}

Order Count: ${order_count}
Last Order Date: ${last_order_date}
Total Annual Spend: ${total_annual_spend}

Develop a comprehensive engagement strategy, including:
1. Recommended frequency and timing of communications
2. Content themes and topics likely to resonate
3. Special offers or incentives that might be effective
4. Loyalty program recommendations
5. Personalization opportunities
6. Specific actions to increase engagement and satisfaction</field>
            <field name="system_prompt">You are a customer engagement strategist who specializes in developing plans to increase customer interaction and loyalty. Create a tailored engagement plan based on the available data. Be specific and actionable in your recommendations.</field>
        </record>

        <!-- Risk Assessment Template -->
        <record id="prompt_template_partner_risk" model="ai.prompt.template">
            <field name="name">Risk Assessment</field>
            <field name="description">Assessment of customer risks and mitigation strategies</field>
            <field name="template_key">risk</field>
            <field name="sequence">60</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="is_default">False</field>
            <field name="is_enabled">True</field>
            <field name="prompt_template">Please assess the risk factors for this contact:

Name: ${name}
Email: ${email}
Phone: ${phone}
Created: ${create_date}

Order Count: ${order_count}
Last Order Date: ${last_order_date}
Total Annual Spend: ${total_annual_spend}

Provide a risk assessment, including:
1. Churn risk level (low, medium, high) with justification
2. Warning signs or red flags in their history
3. Competitive threat assessment
4. Payment or credit risk factors if applicable
5. Recommended risk mitigation strategies
6. Specific retention actions if churn risk is medium or high</field>
            <field name="system_prompt">You are a risk assessment specialist who identifies potential customer risks and develops mitigation strategies. Analyze the available data to assess various risk factors and provide actionable recommendations to address them.</field>
        </record>
    </data>
</odoo>
