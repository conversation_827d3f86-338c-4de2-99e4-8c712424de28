<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.chatter.ai</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="inside">
                <app string="Chatter AI" name="itms_chatter_ai">
                    <block title="AI Settings">
                        <setting string="Default Groq Model" help="Select the default Groq model to use for AI suggestions">
                            <field name="groq_default_model"/>

                            <!-- Model Statistics -->
                            <div class="mt-3 p-3 bg-light rounded">
                                <h6 class="mb-2">Selected Model Statistics</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <strong>Speed:</strong>
                                        <field name="model_speed" readonly="1"/> tokens/s
                                    </div>
                                    <div class="col-6">
                                        <strong>Context Length:</strong>
                                        <field name="model_context_length" readonly="1"/> tokens
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-6">
                                        <strong>Input Price:</strong>
                                        $<field name="model_input_price" readonly="1"/> /M tokens
                                    </div>
                                    <div class="col-6">
                                        <strong>Output Price:</strong>
                                        $<field name="model_output_price" readonly="1"/> /M tokens
                                    </div>
                                </div>
                            </div>
                        </setting>

                        <setting string="AI Enabled Models" help="Select models to enable AI integration">
                            <field name="ai_enabled_model_ids" widget="many2many_tags"/>
                        </setting>
                    </block>
                </app>
            </xpath>
        </field>
    </record>
</odoo>
