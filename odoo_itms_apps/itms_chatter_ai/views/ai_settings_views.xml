<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_ai_model_settings_tree" model="ir.ui.view">
        <field name="name">ai.model.settings.tree</field>
        <field name="model">ai.model.settings</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="model_name"/>
                <field name="is_enabled"/>
            </list>
        </field>
    </record>

    <record id="view_ai_model_settings_form" model="ir.ui.view">
        <field name="name">ai.model.settings.form</field>
        <field name="model">ai.model.settings</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="model_id"/>
                            <field name="model_name"/>
                            <field name="is_enabled"/>
                        </group>
                        <group>
                            <field name="system_prompt" placeholder="Set the context for the AI model"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Prompt Templates" name="prompt_templates">
                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <group>
                                        <field name="prompt_template_id" options="{'no_create': False, 'no_open': False}"/>
                                    </group>
                                </div>
                                <div class="col-sm-6 text-right">
                                    <button name="apply_prompt_template" type="object" string="Apply Selected Template"
                                            class="btn btn-primary" invisible="prompt_template_id == False"/>
                                </div>
                            </div>

                            <label for="prompt_template_ids" string="Prompt Templates"/>
                            <div class="o_row">
                                <field name="prompt_template_ids" widget="many2many_tags" readonly="1"/>
                                <button name="open_prompt_templates" type="object" string="Manage Templates" class="btn btn-link">
                                    <i class="fa fa-external-link"/> Manage Templates
                                </button>
                            </div>

                            <group string="Current Prompt Template" invisible="prompt_template == False">
                                <field name="prompt_template" widget="text" nolabel="1"/>
                            </group>
                        </page>
                        <page string="Data Query" name="query">
                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <group>
                                        <field name="preset_query_template" widget="radio" options="{'horizontal': true}"/>
                                    </group>
                                </div>
                                <div class="col-sm-6 text-right">
                                    <button name="apply_query_builder" type="object" string="Apply Builder Query"
                                            class="btn btn-primary" invisible="query_builder_output == False"/>
                                    <button name="test_query" type="object" string="Test Query"
                                            class="btn btn-success" invisible="data_query == False"/>
                                    <button name="save_query_results" type="object" string="Save Results"
                                            class="btn btn-info" invisible="data_query == False"/>
                                </div>
                            </div>
                            <field name="data_query"
                                   widget="ace"
                                   placeholder="SQL query for data gathering. Must be SELECT only."
                                   nolabel="1"/>

                            <!-- Saved Query Results Section -->
                            <div class="mt-4" invisible="saved_query_results == False">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h5 class="mb-0">Saved Query Results</h5>
                                    <div>
                                        <field name="saved_query_title" placeholder="Title for saved results" class="d-inline-block me-2"/>
                                        <button name="clear_saved_results" type="object" string="Clear"
                                                class="btn btn-sm btn-secondary"/>
                                    </div>
                                </div>
                                <field name="saved_query_results" readonly="1" nolabel="1" widget="html"/>
                            </div>
                        </page>
                        <page string="Query Builder" name="query_builder">
                            <p class="alert alert-info">Build your query by selecting fields, join conditions, and filters. The SQL will be generated automatically.</p>
                            <group string="Fields">
                                <field name="selected_fields" widget="field_selector" placeholder="Select fields to include"/>
                            </group>
                            <group string="Joins">
                                <field name="join_tables" placeholder="[{'table': 'res_users', 'condition': 'rp.user_id = ru.id', 'type': 'LEFT JOIN'}]"/>
                            </group>
                            <group string="Filters">
                                <field name="filter_conditions" placeholder="[{'field': 'active', 'operator': '=', 'value': 'true'}]"/>
                            </group>
                            <group string="Sorting and Limiting">
                                <field name="order_by"/>
                                <field name="limit"/>
                            </group>
                            <group string="Generated SQL">
                                <field name="query_builder_output" widget="ace" nolabel="1"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_ai_model_settings" model="ir.actions.act_window">
        <field name="name">AI Model Settings</field>
        <field name="res_model">ai.model.settings</field>
        <field name="view_mode">list,form</field>
    </record>

    <menuitem id="menu_ai_model_settings"
              name="AI Model Settings"
              parent="menu_ai_settings"
              action="action_ai_model_settings"
              sequence="10"
              groups="base.group_system"/>
</odoo>
