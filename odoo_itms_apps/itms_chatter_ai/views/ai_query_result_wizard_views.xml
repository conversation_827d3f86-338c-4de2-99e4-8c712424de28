<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_ai_query_result_wizard_form" model="ir.ui.view">
        <field name="name">ai.query.result.wizard.form</field>
        <field name="model">ai.query.result.wizard</field>
        <field name="arch" type="xml">
            <form string="Save Query Results">
                <group>
                    <field name="ai_settings_id" invisible="1"/>
                    <field name="title" placeholder="Enter a title for the query results"/>
                    <field name="record_id" invisible="1"/>
                </group>
                <footer>
                    <button name="action_save" string="Save" type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>
