<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- List View -->
    <record id="view_ai_prompt_template_list" model="ir.ui.view">
        <field name="name">ai.prompt.template.list</field>
        <field name="model">ai.prompt.template</field>
        <field name="arch" type="xml">
            <list string="AI Prompt Templates">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="template_key"/>
                <field name="model_id"/>
                <field name="is_default"/>
                <field name="is_enabled"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_ai_prompt_template_form" model="ir.ui.view">
        <field name="name">ai.prompt.template.form</field>
        <field name="model">ai.prompt.template</field>
        <field name="arch" type="xml">
            <form string="AI Prompt Template">
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name" placeholder="Template Name"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="template_key"/>
                            <field name="model_id"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="is_default"/>
                            <field name="is_enabled"/>
                        </group>
                    </group>
                    <group>
                        <field name="description" placeholder="Brief description of this template's purpose"/>
                    </group>
                    <notebook>
                        <page string="Prompt Template">
                            <div class="alert alert-info mb-3">
                                <p><strong>Placeholder Syntax:</strong> Use field names in curly braces with dollar sign to include field values.</p>
                                <p><strong>Examples:</strong> For name field use dollar-left_brace-name-right_brace, for email use dollar-left_brace-email-right_brace</p>
                            </div>
                            <field name="prompt_template" widget="text" placeholder="Enter your prompt template here..." nolabel="1"/>
                        </page>
                        <page string="System Prompt">
                            <div class="alert alert-info mb-3">
                                <p>The system prompt provides context and instructions to the AI model.</p>
                                <p>It's optional but can help improve the quality of responses.</p>
                            </div>
                            <field name="system_prompt" widget="text" placeholder="Enter optional system prompt here..." nolabel="1"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_ai_prompt_template_search" model="ir.ui.view">
        <field name="name">ai.prompt.template.search</field>
        <field name="model">ai.prompt.template</field>
        <field name="arch" type="xml">
            <search string="Search AI Prompt Templates">
                <field name="name"/>
                <field name="template_key"/>
                <field name="model_id"/>
                <filter string="Default Templates" name="default" domain="[('is_default', '=', True)]"/>
                <filter string="Enabled" name="enabled" domain="[('is_enabled', '=', True)]"/>
                <group expand="0" string="Group By">
                    <filter string="Model" name="group_by_model" context="{'group_by': 'model_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_ai_prompt_template" model="ir.actions.act_window">
        <field name="name">AI Prompt Templates</field>
        <field name="res_model">ai.prompt.template</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_ai_prompt_template_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first AI prompt template!
            </p>
            <p>
                Define templates for different types of AI analysis.
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_ai_prompt_template"
              name="Prompt Templates"
              parent="menu_ai_settings"
              action="action_ai_prompt_template"
              sequence="20"
              groups="base.group_system"/>
</odoo>
