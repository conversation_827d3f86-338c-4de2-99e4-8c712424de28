import logging

from odoo import http
from odoo.http import request

_logger = logging.getLogger(__name__)


class ActivitySuggestionsController(http.Controller):
    @http.route("/itms_chatter_ai/activity_suggestions", type="json", auth="user")
    def get_activity_suggestions(self, model, res_id, prompt_type="follow_up"):
        """Get activity suggestions for a record

        Args:
            model: The model name
            res_id: The record ID
            prompt_type: The type of prompt to use

        Returns:
            dict: JSON-formatted suggestions
        """
        try:
            # Convert res_id to integer
            res_id = int(res_id)

            # Check if the user has access to the record
            record = request.env[model].browse(res_id).exists()
            if not record:
                return {
                    "success": False,
                    "error": f"Record {model} #{res_id} not found or access denied",
                }

            # Get activity suggestions
            result = request.env[
                "activity.suggestion.service"
            ].get_ai_suggestions_as_json(model, res_id, prompt_type)

            return result
        except Exception as e:
            _logger.error(f"Error getting activity suggestions: {str(e)}")
            return {"success": False, "error": str(e), "suggestions": []}

    @http.route("/itms_chatter_ai/create_activity", type="json", auth="user")
    def create_activity(self, model, res_id, suggestion):
        """Create an activity from a suggestion

        Args:
            model: The model name
            res_id: The record ID
            suggestion: The suggestion to create an activity from

        Returns:
            dict: Result of the activity creation
        """
        try:
            # Convert res_id to integer
            res_id = int(res_id)

            # Check if the user has access to the record
            record = request.env[model].browse(res_id).exists()
            if not record:
                return {
                    "success": False,
                    "error": f"Record {model} #{res_id} not found or access denied",
                }

            # Create activity
            result = (
                request.env["activity.suggestion.service"]
                .with_user(request.env.user)
                .create_activity_from_suggestion(model, res_id, suggestion)
            )

            # Log the result for debugging
            _logger.info(f"Activity creation result: {result}")

            return result
        except Exception as e:
            _logger.error(f"Error creating activity: {str(e)}")
            return {"success": False, "error": str(e)}
