{
    "name": "Chatter - ITMS AI Extended",
    "version": "********.0",
    "summary": "AI-Enhanced Chatter with Groq Integration",
    "description": "...",
    "author": "ITMS Group",
    "website": "http://www.itmsgroup.com.au",
    "category": "ITMS",
    "depends": ["base", "mail", "itms_chatter", "sale", "web", "crm"],
    "data": [
        # Security
        "security/chatter_ai_security.xml",
        "security/ir.model.access.csv",
        # Menus
        "views/menu_views.xml",
        # Models and Views
        "views/ai_settings_views.xml",
        "views/ai_query_result_wizard_views.xml",
        "views/res_config_settings_views.xml",
        "views/res_partner_views.xml",
        "views/client_analysis_views.xml",
        "views/crm_opportunity_analysis_views.xml",
        # Prompt Template Model and Security
        "views/ai_prompt_template_views.xml",
        "views/ai_prompt_template_actions.xml",
        # Data
        "data/ai_model_settings_data.xml",
        "data/ai_crm_settings_data.xml",
        "data/ai_prompt_template_data.xml",
        "data/ai_crm_prompt_templates.xml",
    ],
    "assets": {
        "web.assets_backend": [
            # CSS
            "itms_chatter_ai/static/src/scss/style.scss",
            # Libraries
            "itms_chatter_ai/static/lib/apexcharts/apexcharts.min.js",
            # AI Components
            "itms_chatter_ai/static/src/components/ai_thread/thread_ai.js",
            "itms_chatter_ai/static/src/components/ai_thread/thread_ai.xml",
            "itms_chatter_ai/static/src/components/client_analysis/**/*",
            "itms_chatter_ai/static/src/components/field_selector/**/*",
            "itms_chatter_ai/static/src/components/activity_suggestions/**/*",
        ],
        "web.assets_frontend": [
            ("include", "web._assets_react"),
        ],
    },
    "license": "LGPL-3",
    "installable": True,
    "application": True,
    "auto_install": False,
    "pre_init_hook": None,
    "post_init_hook": None,
    "uninstall_hook": None,
    "post_load": None,
    "pre_load": None,
    "version_upgradable": True,
}
