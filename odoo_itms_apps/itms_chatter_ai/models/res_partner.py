import logging
from datetime import date, datetime

from dateutil.relativedelta import relativedelta
from odoo import _, api, fields, models

_logger = logging.getLogger(__name__)


class ResPartner(models.Model):
    _inherit = "res.partner"

    total_annual_spend = fields.Monetary(
        string="Total Annual Spend",
        compute="_compute_spend_metrics",
        store=True,
        help="Total amount spent in the current year",
    )
    total_monthly_spend = fields.Monetary(
        string="Total Monthly Spend",
        compute="_compute_spend_metrics",
        store=True,
        help="Total amount spent in the current month",
    )
    average_order_value = fields.Monetary(
        string="Average Order Value",
        compute="_compute_spend_metrics",
        store=True,
        help="Average amount per order this year",
    )
    order_count_ytd = fields.Integer(
        string="Orders This Year",
        compute="_compute_spend_metrics",
        store=True,
        help="Number of orders placed this year",
    )
    last_order_date = fields.Date(
        string="Last Order Date",
        compute="_compute_spend_metrics",
        store=True,
        help="Date of the most recent order",
    )
    currency_id = fields.Many2one(
        "res.currency",
        string="Currency",
        default=lambda self: self.env.company.currency_id.id,
    )

    @api.depends(
        "sale_order_ids",
        "sale_order_ids.amount_total",
        "sale_order_ids.date_order",
        "sale_order_ids.state",
    )
    def _compute_spend_metrics(self):
        today = date.today()
        year_start = date(today.year, 1, 1)
        month_start = date(today.year, today.month, 1)

        for partner in self:
            _logger.info(
                f"Computing spend metrics for partner {partner.id} ({partner.name})"
            )
            base_domain = [
                ("partner_id", "=", partner.id),
                ("state", "in", ["sale", "done"]),
            ]

            year_domain = base_domain + [
                ("date_order", ">=", year_start.strftime("%Y-%m-%d")),
                (
                    "date_order",
                    "<",
                    (year_start + relativedelta(years=1)).strftime("%Y-%m-%d"),
                ),
            ]
            year_orders = self.env["sale.order"].search(year_domain)
            _logger.info(f"Year orders for {partner.name}: {len(year_orders)} found")

            month_domain = base_domain + [
                ("date_order", ">=", month_start.strftime("%Y-%m-%d")),
                (
                    "date_order",
                    "<",
                    (month_start + relativedelta(months=1)).strftime("%Y-%m-%d"),
                ),
            ]
            month_orders = self.env["sale.order"].search(month_domain)
            _logger.info(f"Month orders for {partner.name}: {len(month_orders)} found")

            partner.total_annual_spend = sum(year_orders.mapped("amount_total"))
            partner.total_monthly_spend = sum(month_orders.mapped("amount_total"))
            partner.order_count_ytd = len(year_orders)
            partner.average_order_value = (
                partner.total_annual_spend / partner.order_count_ytd
                if partner.order_count_ytd
                else 0
            )

            all_orders = self.env["sale.order"].search(
                base_domain, order="date_order desc", limit=1
            )
            partner.last_order_date = (
                all_orders.date_order.date() if all_orders else False
            )
            _logger.info(
                f"Metrics for {partner.name}: Annual Spend={partner.total_annual_spend}, Monthly Spend={partner.total_monthly_spend}, Order Count={partner.order_count_ytd}, Avg Order={partner.average_order_value}, Last Order={partner.last_order_date}"
            )

    def action_view_sales(self):
        """Smart button action to view sales orders"""
        self.ensure_one()
        _logger.info(f"Viewing sales for partner {self.id} ({self.name})")
        action = self.env["ir.actions.actions"]._for_xml_id(
            "sale.act_res_partner_2_sale_order"
        )
        action["domain"] = [
            ("partner_id", "child_of", self.ids),
            ("state", "in", ["sale", "done"]),
        ]
        _logger.info(f"Sales action domain: {action['domain']}")
        return action
