import json
import logging
import re
from datetime import datetime, timedelta

from odoo import _, api, fields, models

_logger = logging.getLogger(__name__)


class ActivitySuggestionService(models.AbstractModel):
    _name = "activity.suggestion.service"
    _description = "AI Activity Suggestion Service"

    # Get the correct activity type IDs
    def _get_activity_type_id(self, activity_type):
        """Get the activity type ID based on the activity type name

        Args:
            activity_type: The activity type name (call, email, meeting, todo)

        Returns:
            int: The activity type ID
        """
        # Map of activity types to their XML IDs
        activity_type_map = {
            "call": "mail.mail_activity_data_call",
            "email": "mail.mail_activity_data_email",  # Corrected from mail to email
            "meeting": "mail.mail_activity_data_meeting",
            "todo": "mail.mail_activity_data_todo",
        }

        # Default to todo if the activity type is not found
        xml_id = activity_type_map.get(activity_type, "mail.mail_activity_data_todo")

        try:
            return self.env.ref(xml_id).id
        except Exception as e:
            _logger.warning(
                f"Activity type {xml_id} not found, using default: {str(e)}"
            )
            # Try to get any activity type as fallback
            activity_type = self.env["mail.activity.type"].search([], limit=1)
            return activity_type.id if activity_type else False

    @api.model
    def get_ai_suggestions_as_json(
        self, model_name, record_id, prompt_type="follow_up"
    ):
        """Get AI suggestions in JSON format for activity creation

        Args:
            model_name: The model name
            record_id: The record ID
            prompt_type: The type of prompt to use (follow_up, etc.)

        Returns:
            dict: JSON-formatted suggestions or error message
        """
        try:
            # Get the system prompt that requests JSON output
            json_system_prompt = self._get_json_system_prompt(prompt_type)

            # Get AI suggestions with the JSON system prompt
            result = self.env[
                "groq.service"
            ].get_ai_suggestions_with_custom_system_prompt(
                model_name, record_id, prompt_type, json_system_prompt
            )

            # Log the result for debugging
            _logger.info(f"AI suggestions result: {result}")

            # Parse the JSON response
            return self._parse_json_response(result)
        except Exception as e:
            _logger.error(f"Error getting AI suggestions as JSON: {str(e)}")
            return {"success": False, "error": str(e), "suggestions": []}

    def _get_json_system_prompt(self, prompt_type):
        """Get the system prompt that requests JSON output

        Args:
            prompt_type: The type of prompt

        Returns:
            str: System prompt requesting JSON output
        """
        if prompt_type == "follow_up":
            return """You are an AI assistant that helps sales and customer service teams with follow-up activities.
Based on the customer information provided, suggest 3 specific follow-up activities.

YOUR RESPONSE MUST BE VALID JSON. Do not include any text before or after the JSON.
The JSON must follow this exact structure:

{
  "suggestions": [
    {
      "activity_type": "call" | "email" | "meeting" | "todo",
      "summary": "Brief summary of the activity (max 64 chars)",
      "note": "Detailed description of what should be done",
      "days_from_now": integer (1-30),
      "priority": "low" | "medium" | "high"
    },
    ...
  ]
}

Guidelines:
1. Provide exactly 3 suggestions
2. Make suggestions specific and actionable
3. Vary the activity types (don't suggest 3 calls)
4. Base suggestions on the customer's history and status
5. Keep summaries concise (under 64 characters)
6. Provide helpful details in the note field
7. Set reasonable timeframes (1-30 days)
8. Assign appropriate priorities based on urgency
9. ONLY RETURN VALID JSON - NO OTHER TEXT BEFORE OR AFTER
10. USE DOUBLE QUOTES FOR ALL PROPERTY NAMES AND STRING VALUES
11. DO NOT USE SINGLE QUOTES IN THE JSON
12. DO NOT USE MARKDOWN CODE BLOCKS"""
        else:
            return """You are an AI assistant that helps business teams with follow-up activities.
Based on the information provided, suggest 3 specific follow-up activities.

YOUR RESPONSE MUST BE VALID JSON. Do not include any text before or after the JSON.
The JSON must follow this exact structure:

{
  "suggestions": [
    {
      "activity_type": "call" | "email" | "meeting" | "todo",
      "summary": "Brief summary of the activity (max 64 chars)",
      "note": "Detailed description of what should be done",
      "days_from_now": integer (1-30),
      "priority": "low" | "medium" | "high"
    },
    ...
  ]
}

Guidelines:
1. Provide exactly 3 suggestions
2. Make suggestions specific and actionable
3. Vary the activity types (don't suggest 3 calls)
4. Base suggestions on the record's history and status
5. Keep summaries concise (under 64 characters)
6. Provide helpful details in the note field
7. Set reasonable timeframes (1-30 days)
8. Assign appropriate priorities based on urgency
9. ONLY RETURN VALID JSON - NO OTHER TEXT BEFORE OR AFTER
10. USE DOUBLE QUOTES FOR ALL PROPERTY NAMES AND STRING VALUES
11. DO NOT USE SINGLE QUOTES IN THE JSON
12. DO NOT USE MARKDOWN CODE BLOCKS"""

    def _parse_json_response(self, response):
        """Parse the JSON response from the AI

        Args:
            response: The response from the AI

        Returns:
            dict: Parsed JSON or error message
        """
        if not response:
            return {
                "success": False,
                "error": "Empty response from AI",
                "suggestions": [],
            }

        # Log the raw response for debugging
        _logger.info(f"Raw AI response: {response}")

        try:
            # Try different approaches to extract JSON

            # First, try to find JSON between triple backticks (common in AI responses)
            json_match = re.search(r"```(?:json)?\s*({[\s\S]*?})\s*```", response)
            if json_match:
                json_str = json_match.group(1)
                _logger.info(f"Extracted JSON from code block: {json_str}")
            else:
                # Try to find any JSON object in the response
                json_match = re.search(r"({[\s\S]*?})(?:\s*$|\n)", response)
                if json_match:
                    json_str = json_match.group(1)
                    _logger.info(f"Extracted JSON from response: {json_str}")
                else:
                    # If no JSON object found, try to create one from the response
                    # This is a fallback for malformed JSON
                    _logger.warning(
                        "No JSON found in response, attempting to create default suggestions"
                    )

                    # Create default suggestions based on the response text
                    return self._create_default_suggestions(response)

            # Clean up the JSON string
            json_str = json_str.strip()

            # Try to parse the JSON
            try:
                data = json.loads(json_str)
            except json.JSONDecodeError:
                # Try to fix common JSON issues
                _logger.warning("JSON parsing failed, attempting to fix JSON")
                json_str = self._fix_json(json_str)
                data = json.loads(json_str)

            # Validate the structure
            if "suggestions" not in data or not isinstance(data["suggestions"], list):
                _logger.warning("Invalid JSON structure: missing suggestions array")
                return {
                    "success": False,
                    "error": "Invalid JSON structure: missing suggestions array",
                    "suggestions": [],
                }

            # Validate each suggestion
            valid_suggestions = []
            for suggestion in data["suggestions"]:
                if self._validate_suggestion(suggestion):
                    valid_suggestions.append(suggestion)

            if not valid_suggestions:
                _logger.warning("No valid suggestions found in JSON")
                return {
                    "success": False,
                    "error": "No valid suggestions found in JSON",
                    "suggestions": [],
                }

            return {"success": True, "suggestions": valid_suggestions}
        except json.JSONDecodeError as e:
            _logger.error(f"JSON decode error: {str(e)}")
            # Try to create default suggestions as a fallback
            return self._create_default_suggestions(response)
        except Exception as e:
            _logger.error(f"Error parsing JSON response: {str(e)}")
            return {"success": False, "error": str(e), "suggestions": []}

    def _fix_json(self, json_str):
        """Attempt to fix common JSON issues

        Args:
            json_str: The JSON string to fix

        Returns:
            str: The fixed JSON string
        """
        # Replace single quotes with double quotes
        json_str = re.sub(r"'([^']*)'\s*:", r'"\1":', json_str)
        json_str = re.sub(r":\s*'([^']*)'(?=[,}])", r':"\1"', json_str)

        # Add quotes around unquoted property names
        json_str = re.sub(r"([{,])\s*(\w+)\s*:", r'\1"\2":', json_str)

        # Fix trailing commas
        json_str = re.sub(r",\s*}", "}", json_str)
        json_str = re.sub(r",\s*]", "]", json_str)

        _logger.info(f"Fixed JSON: {json_str}")
        return json_str

    def _create_default_suggestions(self, response):
        """Create default suggestions from the response text

        Args:
            response: The response text

        Returns:
            dict: Default suggestions
        """
        _logger.info("Creating default suggestions from response text")

        # Extract potential activities from the text
        activities = []

        # Look for numbered lists (1. Do something)
        numbered_items = re.findall(r"\d+\.\s*([^\n]+)", response)
        if numbered_items:
            for item in numbered_items[:3]:  # Take up to 3 items
                activities.append(
                    {
                        "activity_type": "todo",
                        "summary": item[:64],  # Limit to 64 chars
                        "note": item,
                        "days_from_now": 7,  # Default to 7 days
                        "priority": "medium",
                    }
                )

        # If we don't have 3 activities yet, look for bullet points
        if len(activities) < 3:
            bullet_items = re.findall(r"[•\-*]\s*([^\n]+)", response)
            for item in bullet_items[: 3 - len(activities)]:  # Fill up to 3 items
                activities.append(
                    {
                        "activity_type": "todo",
                        "summary": item[:64],  # Limit to 64 chars
                        "note": item,
                        "days_from_now": 7,  # Default to 7 days
                        "priority": "medium",
                    }
                )

        # If we still don't have 3 activities, create generic ones
        activity_types = ["call", "email", "todo"]
        while len(activities) < 3:
            idx = len(activities)
            activities.append(
                {
                    "activity_type": activity_types[idx % len(activity_types)],
                    "summary": f"Follow up {idx + 1}",
                    "note": f"AI suggested to follow up with this contact. Original response: {response[:200]}...",
                    "days_from_now": (idx + 1) * 7,  # 7, 14, 21 days
                    "priority": "medium",
                }
            )

        return {"success": True, "suggestions": activities}

    def _validate_suggestion(self, suggestion):
        """Validate a suggestion

        Args:
            suggestion: The suggestion to validate

        Returns:
            bool: True if valid, False otherwise
        """
        required_fields = [
            "activity_type",
            "summary",
            "note",
            "days_from_now",
            "priority",
        ]
        for field in required_fields:
            if field not in suggestion:
                return False

        # Validate activity_type
        valid_activity_types = ["call", "email", "meeting", "todo"]
        if suggestion["activity_type"] not in valid_activity_types:
            return False

        # Validate summary length
        if len(suggestion["summary"]) > 64:
            suggestion["summary"] = suggestion["summary"][:64]

        # Validate days_from_now
        try:
            days = int(suggestion["days_from_now"])
            if days < 1 or days > 30:
                suggestion["days_from_now"] = max(1, min(30, days))
        except (ValueError, TypeError):
            suggestion["days_from_now"] = 7

        # Validate priority
        if suggestion["priority"] not in ["low", "medium", "high"]:
            suggestion["priority"] = "medium"

        return True

    @api.model
    def create_activity_from_suggestion(self, model_name, record_id, suggestion):
        """Create an activity from a suggestion

        Args:
            model_name: The model name
            record_id: The record ID
            suggestion: The suggestion to create an activity from

        Returns:
            dict: Result of the activity creation
        """
        try:
            record = self.env[model_name].browse(record_id)
            if not record.exists():
                return {
                    "success": False,
                    "error": f"Record {model_name} #{record_id} not found",
                }

            # Get the activity type
            activity_type_id = self._get_activity_type_id(suggestion["activity_type"])
            if not activity_type_id:
                return {
                    "success": False,
                    "error": f"Activity type '{suggestion['activity_type']}' not found",
                }

            # Calculate the due date
            days_from_now = int(suggestion["days_from_now"])
            due_date = fields.Date.today() + timedelta(days=days_from_now)

            # Create the activity
            activity_vals = {
                "activity_type_id": activity_type_id,
                "summary": suggestion["summary"],
                "note": suggestion["note"],
                "date_deadline": due_date,
                "user_id": self.env.user.id,
                "res_model_id": self.env["ir.model"]
                .search([("model", "=", model_name)], limit=1)
                .id,
                "res_id": record_id,
            }

            # Add priority if the field exists on the model
            if "priority" in self.env["mail.activity"]._fields:
                priority_map = {"low": "0", "medium": "1", "high": "2"}
                activity_vals["priority"] = priority_map.get(
                    suggestion["priority"], "1"
                )

            # Log the activity values for debugging
            _logger.info(f"Creating activity with values: {activity_vals}")

            # Create the activity
            try:
                activity = self.env["mail.activity"].create(activity_vals)
                _logger.info(f"Created activity: {activity.id}")
                return {
                    "success": True,
                    "activity_id": activity.id,
                    "message": f"Activity '{suggestion['summary']}' created successfully",
                }
            except Exception as e:
                _logger.error(f"Error creating activity: {str(e)}")
                return {"success": False, "error": str(e)}

            # Return is handled in the try/except block above
        except Exception as e:
            _logger.error(f"Error creating activity: {str(e)}")
            return {"success": False, "error": str(e)}

    @api.model
    def create_activities_from_suggestions(self, model_name, record_id, suggestions):
        """Create activities from multiple suggestions

        Args:
            model_name: The model name
            record_id: The record ID
            suggestions: List of suggestions to create activities from

        Returns:
            dict: Result of the activities creation
        """
        results = []
        success_count = 0

        for suggestion in suggestions:
            result = self.create_activity_from_suggestion(
                model_name, record_id, suggestion
            )
            results.append(result)
            if result.get("success"):
                success_count += 1

        return {
            "success": success_count > 0,
            "created_count": success_count,
            "total_count": len(suggestions),
            "results": results,
        }
