import json

from odoo import api, fields, models
from odoo.exceptions import ValidationError


class AIModelSettings(models.Model):
    _name = "ai.model.settings"
    _description = "AI Model Settings"

    name = fields.Char(compute="_compute_name", store=True)
    model_id = fields.Many2one(
        "ir.model", string="Model", required=True, ondelete="cascade"
    )
    model_name = fields.Char(
        related="model_id.model", string="Technical Name", readonly=True
    )
    is_enabled = fields.Boolean("Enable AI", default=False)
    prompt_template = fields.Text("Prompt Template")
    prompt_template_id = fields.Many2one(
        "ai.prompt.template",
        string="Active Prompt Template",
        domain="[('model_name', '=', model_name), ('is_enabled', '=', True)]",
        help="Select the active prompt template",
    )
    prompt_template_ids = fields.Many2many(
        "ai.prompt.template",
        string="Prompt Templates",
        domain="[('model_name', '=', model_name)]",
        help="Prompt templates for this model",
    )
    data_query = fields.Text("Data Query")
    system_prompt = fields.Text("System Prompt")

    # New fields for preset templates
    preset_query_template = fields.Selection(
        [
            ("none", "Custom Query"),
            ("basic", "Basic Fields Only"),
            ("standard", "Standard Analysis"),
            ("advanced", "Advanced Analysis with Joins"),
        ],
        string="Query Template",
        default="none",
        help="Select a predefined query template",
    )

    # Fields for the query builder
    selected_fields = fields.Char(
        string="Selected Fields", help="JSON-encoded list of selected fields"
    )
    join_tables = fields.Char(
        string="Join Tables", help="JSON-encoded list of tables to join"
    )
    filter_conditions = fields.Char(
        string="Filter Conditions", help="JSON-encoded list of filter conditions"
    )
    order_by = fields.Char(string="Order By", help="Field to order results by")
    limit = fields.Integer(
        string="Limit", default=100, help="Maximum number of records to return"
    )
    query_builder_output = fields.Text(string="Generated SQL", readonly=True)

    # Fields for saved query results
    saved_query_results = fields.Text(
        string="Saved Query Results",
        readonly=True,
        help="JSON-encoded saved query results",
    )
    saved_query_title = fields.Char(
        string="Saved Query Title", help="Title for the saved query results"
    )

    _sql_constraints = [
        ("unique_model", "unique(model_id)", "Settings already exist for this model!")
    ]

    @api.depends("model_id")
    def _compute_name(self):
        for record in self:
            record.name = record.model_id.name if record.model_id else ""

    @api.onchange("model_id")
    def _onchange_model_id(self):
        if self.model_id:
            # Clear prompt templates when model changes
            self.prompt_template_ids = [(5, 0, 0)]
            self.prompt_template_id = False

    @api.constrains("data_query")
    def _check_data_query(self):
        for record in self:
            if record.data_query:
                if not record.data_query.lower().startswith("select"):
                    raise ValidationError("Data query must be a SELECT statement")
                if (
                    "delete" in record.data_query.lower()
                    or "update" in record.data_query.lower()
                    or "insert" in record.data_query.lower()
                ):
                    raise ValidationError("Only SELECT queries are allowed")

    @api.onchange("preset_query_template")
    def _onchange_preset_query_template(self):
        if self.preset_query_template and self.preset_query_template != "none":
            self.data_query = self._get_template_query()

    @api.onchange("prompt_template_id")
    def _onchange_prompt_template_id(self):
        if self.prompt_template_id:
            self.prompt_template = self.prompt_template_id.prompt_template
            if self.prompt_template_id.system_prompt:
                self.system_prompt = self.prompt_template_id.system_prompt

    def apply_prompt_template(self):
        """Apply the selected prompt template"""
        self.ensure_one()
        if self.prompt_template_id:
            self.prompt_template = self.prompt_template_id.prompt_template
            if self.prompt_template_id.system_prompt:
                self.system_prompt = self.prompt_template_id.system_prompt
            return self._show_notification(
                "Success", "Prompt template applied successfully.", "success"
            )

    def action_open_templates(self):
        """Open the prompt templates view"""
        self.ensure_one()
        return {
            "type": "ir.actions.act_window",
            "name": "Prompt Templates",
            "res_model": "ai.prompt.template",
            "view_mode": "list,form",
            "domain": [("model_name", "=", self.model_name)],
            "context": {
                "default_model_id": self.model_id.id,
                "default_ai_settings_id": self.id,
                "default_model_name": self.model_name,
            },
            "target": "current",
        }

    def open_prompt_templates(self):
        """Button method to open prompt templates"""
        self.ensure_one()
        return {
            "type": "ir.actions.act_window",
            "name": "Prompt Templates",
            "res_model": "ai.prompt.template",
            "view_mode": "list,form",
            "domain": [("model_name", "=", self.model_name)],
            "context": {
                "default_model_id": self.model_id.id,
                "default_ai_settings_id": self.id,
                "default_model_name": self.model_name,
            },
            "target": "current",
        }

    def _get_template_query(self):
        """Returns a template query based on the model and selected template"""
        if not self.model_id:
            return ""

        model_name = self.model_id.model
        table_name = model_name.replace(".", "_")

        # Templates for common models
        templates = {
            "res.partner": {
                "basic": f"""
SELECT
    rp.name,
    rp.email,
    rp.phone
FROM
    {table_name} rp
WHERE
    rp.id = %(res_id)s
""",
                "standard": f"""
SELECT
    rp.name,
    rp.email,
    rp.phone,
    rp.create_date,
    COALESCE(
        (SELECT COUNT(*) FROM sale_order
         WHERE partner_id = rp.id AND state in ('sale', 'done'))
    , 0) as order_count,
    COALESCE(
        (SELECT MAX(date_order) FROM sale_order
         WHERE partner_id = rp.id AND state in ('sale', 'done'))
    , NULL) as last_order_date
FROM
    {table_name} rp
WHERE
    rp.id = %(res_id)s
""",
                "advanced": f"""
SELECT
    rp.name,
    rp.email,
    rp.phone,
    COALESCE(
        (SELECT SUM(amount_total) FROM sale_order
         WHERE partner_id = rp.id AND state in ('sale', 'done')
         AND EXTRACT(YEAR FROM date_order) = EXTRACT(YEAR FROM CURRENT_DATE))
    , 0) as total_annual_spend,
    COALESCE(
        (SELECT SUM(amount_total) FROM sale_order
         WHERE partner_id = rp.id AND state in ('sale', 'done')
         AND EXTRACT(MONTH FROM date_order) = EXTRACT(MONTH FROM CURRENT_DATE)
         AND EXTRACT(YEAR FROM date_order) = EXTRACT(YEAR FROM CURRENT_DATE))
    , 0) as total_monthly_spend,
    COALESCE(
        (SELECT AVG(amount_total) FROM sale_order
         WHERE partner_id = rp.id AND state in ('sale', 'done'))
    , 0) as average_order_value,
    (SELECT COUNT(*) FROM sale_order
     WHERE partner_id = rp.id
     AND state in ('sale', 'done')
     AND EXTRACT(YEAR FROM date_order) = EXTRACT(YEAR FROM CURRENT_DATE)
    ) as order_count_ytd,
    (SELECT MAX(date_order) FROM sale_order
     WHERE partner_id = rp.id AND state in ('sale', 'done')
    ) as last_order_date,
    (SELECT COUNT(*) FROM mail_message
     WHERE model='res.partner' AND res_id=rp.id AND message_type='email'
    ) as email_count
FROM
    {table_name} rp
WHERE
    rp.id = %(res_id)s
""",
            },
            "sale.order": {
                "basic": f"""
SELECT
    so.name,
    so.date_order,
    so.amount_total
FROM
    {table_name} so
WHERE
    so.id = %(res_id)s
""",
                "standard": f"""
SELECT
    so.name,
    so.date_order,
    so.amount_total,
    so.amount_untaxed,
    so.amount_tax,
    rp.name as customer_name
FROM
    {table_name} so
JOIN
    res_partner rp ON so.partner_id = rp.id
WHERE
    so.id = %(res_id)s
""",
                "advanced": f"""
SELECT
    so.name,
    so.date_order,
    so.amount_total,
    so.amount_untaxed,
    so.amount_tax,
    rp.name as customer_name,
    rp.email as customer_email,
    COUNT(sol.id) as line_count,
    AVG(sol.price_unit) as avg_product_price,
    (SELECT COUNT(*) FROM sale_order
     WHERE partner_id = so.partner_id
     AND state in ('sale', 'done')
     AND id != so.id) as previous_order_count
FROM
    {table_name} so
JOIN
    res_partner rp ON so.partner_id = rp.id
JOIN
    sale_order_line sol ON sol.order_id = so.id
WHERE
    so.id = %(res_id)s
GROUP BY
    so.id, so.name, so.date_order, so.amount_total, so.amount_untaxed, so.amount_tax, rp.name, rp.email
""",
            },
        }

        # Default template for any model
        default_templates = {
            "basic": f"""
SELECT
    *
FROM
    {table_name}
WHERE
    id = %(res_id)s
""",
            "standard": f"""
SELECT
    t.*,
    t.create_date,
    t.write_date
FROM
    {table_name} t
WHERE
    t.id = %(res_id)s
""",
            "advanced": f"""
SELECT
    t.*,
    u.name as created_by,
    t.create_date
FROM
    {table_name} t
LEFT JOIN
    res_users u ON t.create_uid = u.id
WHERE
    t.id = %(res_id)s
""",
        }

        # Get template for the specific model or fallback to default
        model_templates = templates.get(model_name, default_templates)
        return model_templates.get(
            self.preset_query_template, default_templates[self.preset_query_template]
        )

    @api.onchange(
        "selected_fields", "join_tables", "filter_conditions", "order_by", "limit"
    )
    def _onchange_query_builder(self):
        """Generate SQL from query builder components"""
        if not self.model_id:
            return

        # Basic validation and setup
        try:
            selected_fields = json.loads(self.selected_fields or "[]")
            join_tables = json.loads(self.join_tables or "[]")
            filter_conditions = json.loads(self.filter_conditions or "[]")
        except:
            self.query_builder_output = "Error parsing JSON fields"
            return

        model_name = self.model_id.model
        table_name = model_name.replace(".", "_")
        table_alias = table_name[0]  # First character as alias

        # Build field list
        if not selected_fields:
            field_list = f"{table_alias}.*"
        else:
            field_list = ", ".join(
                [f"{table_alias}.{field}" for field in selected_fields]
            )

        # Build join clauses
        join_clauses = ""
        for join in join_tables:
            if isinstance(join, dict) and "table" in join and "condition" in join:
                join_table = join["table"].replace(".", "_")
                join_alias = join_table[0]  # First character as alias
                join_type = join.get("type", "LEFT JOIN")
                join_clauses += (
                    f"\n{join_type} {join_table} {join_alias} ON {join['condition']}"
                )

        # Build where clause
        where_clause = f"{table_alias}.id = %(res_id)s"
        for condition in filter_conditions:
            if (
                isinstance(condition, dict)
                and "field" in condition
                and "operator" in condition
                and "value" in condition
            ):
                field = condition["field"]
                operator = condition["operator"]
                value = condition["value"]
                where_clause += f" AND {table_alias}.{field} {operator} {value}"

        # Build order by clause
        order_clause = ""
        if self.order_by:
            order_clause = f"\nORDER BY {table_alias}.{self.order_by}"

        # Build limit clause
        limit_clause = ""
        if self.limit:
            limit_clause = f"\nLIMIT {self.limit}"

        # Assemble the query
        query = f"""
SELECT
    {field_list}
FROM
    {table_name} {table_alias}{join_clauses}
WHERE
    {where_clause}{order_clause}{limit_clause}
"""

        self.query_builder_output = query

    def apply_query_builder(self):
        """Apply the generated query to the data_query field"""
        if self.query_builder_output:
            self.data_query = self.query_builder_output
            self.preset_query_template = (
                "none"  # Set to custom since we're using the built query
            )

    def _format_query_result(self, result, record=None):
        """Format a query result as an HTML table"""
        if not result:
            return '<div class="alert alert-warning">No results found.</div>'

        formatted_result = '<table class="table table-sm table-bordered">'
        formatted_result += "<thead><tr>"
        for key in result.keys():
            formatted_result += f"<th>{key}</th>"
        formatted_result += "</tr></thead>"

        formatted_result += "<tbody><tr>"
        for value in result.values():
            if value is None:
                formatted_result += "<td><em>NULL</em></td>"
            elif hasattr(value, "strftime"):
                formatted_result += f'<td>{value.strftime("%Y-%m-%d %H:%M:%S")}</td>'
            else:
                formatted_result += f"<td>{value}</td>"
        formatted_result += "</tr></tbody></table>"

        if record:
            formatted_result = (
                f'<div class="mb-2"><strong>Record:</strong> {record.display_name}</div>'
                + formatted_result
            )

        return formatted_result

    def _execute_query(self, record_id=None):
        """Execute the query and return the result"""
        if not self.data_query:
            return None, "Please define a data query first."

        try:
            # Get a record to test with if not provided
            record = None
            if record_id:
                record = self.env[self.model_name].browse(record_id)
                if not record.exists():
                    return None, f"Record with ID {record_id} not found."
            else:
                record = self.env[self.model_name].search([], limit=1)
                if not record:
                    return (
                        None,
                        f"No records found for model {self.model_name} to test with.",
                    )

            # Execute the query
            self.env.cr.execute(self.data_query, {"res_id": record.id})
            result = self.env.cr.dictfetchone()

            if not result:
                return None, "The query executed successfully but returned no results."

            return result, record

        except Exception as e:
            return None, f"Error executing query: {str(e)}"

    def test_query(self):
        """Test the current query and show results"""
        self.ensure_one()

        result, message_or_record = self._execute_query()

        if not result:
            return self._show_notification("Query Error", message_or_record, "warning")

        # Format the result for display
        formatted_result = self._format_query_result(result, message_or_record)

        return self._show_notification(
            f"Query Results", formatted_result, "success", sticky=True
        )

    def save_query_results(self):
        """Save the query results to be displayed in the chatter"""
        self.ensure_one()

        # First, test the query to make sure it works
        result, message_or_record = self._execute_query()

        if not result:
            return self._show_notification("Query Error", message_or_record, "warning")

        # Show a dialog to get the title
        return {
            "type": "ir.actions.act_window",
            "name": "Save Query Results",
            "res_model": "ai.query.result.wizard",
            "view_mode": "form",
            "target": "new",
            "context": {
                "default_ai_settings_id": self.id,
            },
        }

    def save_query_results_with_title(self, title, record_id=None):
        """Save the query results with the given title"""
        self.ensure_one()

        result, message_or_record = self._execute_query(record_id)

        if not result:
            return self._show_notification("Query Error", message_or_record, "warning")

        # Format the result for display
        formatted_result = self._format_query_result(
            result,
            message_or_record if hasattr(message_or_record, "display_name") else None,
        )

        # Save the results
        self.write(
            {
                "saved_query_results": formatted_result,
                "saved_query_title": title or "Query Results",
            }
        )

        # Create a message in the chatter
        if record_id:
            record = self.env[self.model_name].browse(record_id)
            if record.exists():
                body = (
                    f'<div class="o_mail_notification">'
                    f'<h3>{title or "Query Results"}</h3>'
                    f"{formatted_result}"
                    f"</div>"
                )
                record.message_post(body=body, subject=title or "Query Results")
                return self._show_notification(
                    "Success", "Query results saved and posted to chatter.", "success"
                )

        return self._show_notification("Success", "Query results saved.", "success")

    def clear_saved_results(self):
        """Clear the saved query results"""
        self.ensure_one()
        self.write(
            {
                "saved_query_results": False,
                "saved_query_title": False,
            }
        )
        return self._show_notification(
            "Success", "Saved query results cleared.", "success"
        )

    def _show_notification(self, title, message, type="info", sticky=False):
        """Helper method to show notifications"""
        return {
            "type": "ir.actions.client",
            "tag": "display_notification",
            "params": {
                "title": title,
                "message": message,
                "sticky": sticky,
                "type": type,
            },
        }
