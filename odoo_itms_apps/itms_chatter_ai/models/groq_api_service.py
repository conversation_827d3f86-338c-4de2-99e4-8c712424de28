import logging

from groq import Groq
from odoo import api, models

_logger = logging.getLogger(__name__)


class GroqApiService(models.AbstractModel):
    _name = "groq.api.service"
    _description = "Groq API Service"

    def _get_api_key(self):
        """Get the Groq API key from system parameters"""
        return (
            self.env["ir.config_parameter"]
            .sudo()
            .get_param(
                "itms_chatter_ai.groq_api_key",
                "********************************************************",  # Default key
            )
        )

    def _get_model_name(self):
        """Get the model name to use for API calls"""
        return (
            self.env["ir.config_parameter"]
            .sudo()
            .get_param("itms_chatter_ai.default_model", "llama-3.1-8b-instant")
        )

    def _call_groq_api(self, messages, max_tokens=512, temperature=0.7):
        """Call the Groq API with the given messages"""
        try:
            client = Groq(api_key=self._get_api_key())
            if not client:
                return "Error: Could not create Groq client"

            model = self._get_model_name()

            response = client.chat.completions.create(
                messages=messages,
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
            )

            return response.choices[0].message.content
        except Exception as e:
            _logger.error(f"Error calling Groq API: {str(e)}")
            return f"Error: {str(e)}"

    @api.model
    def test_connection(self):
        """Test connection to Groq API"""
        _logger.info("Testing Groq connection...")
        try:
            client = Groq(api_key=self._get_api_key())
            response = client.chat.completions.create(
                messages=[{"role": "user", "content": "test"}],
                model="llama-3.1-8b-instant",
                max_tokens=1,
            )
            _logger.info("Connection successful!")
            return {
                "success": True,
                "message": "Connected successfully",
                "details": {
                    "api_key_status": "Valid",
                    "client_status": "Connected",
                    "model": "llama-3.1-8b-instant",
                    "endpoint": "https://api.groq.com/openai/v1/chat/completions",
                },
            }
        except Exception as e:
            _logger.error(f"Connection failed: {str(e)}")
            return {
                "success": False,
                "message": str(e),
                "details": {
                    "api_key_status": "Error",
                    "client_status": "Failed",
                    "endpoint": "https://api.groq.com/openai/v1/chat/completions",
                    "error_details": str(e),
                },
            }
