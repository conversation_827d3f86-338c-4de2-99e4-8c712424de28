import logging

from odoo import api, models

_logger = logging.getLogger(__name__)


class PromptService(models.AbstractModel):
    _name = "prompt.service"
    _description = "Prompt Processing Service"

    def _get_prompt_template(self, model_name, prompt_type, ai_settings):
        """Get the appropriate prompt template

        Args:
            model_name: The model name
            prompt_type: The type of prompt to use
            ai_settings: The AI settings record

        Returns:
            tuple: (prompt_template, system_prompt)
        """
        try:
            prompt_template = ai_settings.prompt_template
            system_prompt = ai_settings.system_prompt

            # Try to get a specific template for this model and prompt type
            template = self.env["ai.prompt.template"].get_template(
                model_name, prompt_type
            )
            if template:
                prompt_template = template.prompt_template
                if template.system_prompt:
                    system_prompt = template.system_prompt

            return prompt_template, system_prompt
        except Exception as e:
            _logger.error(f"Error getting prompt template: {str(e)}")
            return ai_settings.prompt_template, ai_settings.system_prompt

    def _replace_placeholders(self, prompt_text, record, data):
        """Replace placeholders in the prompt text with actual values

        Args:
            prompt_text: The prompt text with placeholders
            record: The record to get field values from
            data: Additional data from queries

        Returns:
            str: The prompt text with placeholders replaced
        """
        try:
            # Replace field placeholders with values from the record
            for field_name in record._fields:
                placeholder = f"${{{field_name}}}"
                if placeholder in prompt_text:
                    field_value = record[field_name]
                    if hasattr(field_value, "name_get") and field_value:
                        try:
                            field_value = field_value.name_get()[0][1]
                        except:
                            field_value = str(field_value.id)
                    prompt_text = prompt_text.replace(
                        placeholder, str(field_value or "")
                    )

            # Replace data query placeholders
            for key, value in data.items():
                placeholder = f"${{{key}}}"
                if placeholder in prompt_text:
                    prompt_text = prompt_text.replace(placeholder, str(value or ""))

            return prompt_text
        except Exception as e:
            _logger.error(f"Error replacing placeholders: {str(e)}")
            return prompt_text

    def _is_model_enabled_for_ai(self, model_name):
        """Check if a model is enabled for AI"""
        try:
            enabled_settings = self.env["ai.model.settings"].search(
                [("is_enabled", "=", True)]
            )
            enabled_models = enabled_settings.mapped("model_name")
            return model_name in enabled_models
        except Exception as e:
            _logger.error(f"Error checking if model is enabled: {str(e)}")
            return False

    def _get_ai_settings(self, model_name):
        """Get AI settings for a model, creating default settings if needed"""
        try:
            ai_settings = self.env["ai.model.settings"].search(
                [("model_name", "=", model_name)], limit=1
            )

            if not ai_settings:
                ai_settings = self._create_default_ai_settings(model_name)

            return ai_settings
        except Exception as e:
            _logger.error(f"Error getting AI settings: {str(e)}")
            return None

    def _create_default_ai_settings(self, model_name):
        """Create default AI settings for a model"""
        _logger.info(f"Creating default AI settings for {model_name}")
        try:
            # Get the model record
            model = self.env["ir.model"].search([("model", "=", model_name)], limit=1)
            if not model:
                _logger.error(f"Model {model_name} not found")
                return None

            # Create the AI settings
            return self.env["ai.model.settings"].create(
                {
                    "model_id": model.id,
                    "is_enabled": True,
                    "prompt_template": f"Please analyze this {model.name} and provide insights:\n\n${{name}}\n\nProvide a summary and recommendations.",
                    "system_prompt": "You are an AI assistant for Odoo ERP. Provide concise, actionable insights based on the data provided.",
                }
            )
        except Exception as e:
            _logger.error(f"Error creating default AI settings: {str(e)}")
            return None

    @api.model
    def get_prompt_info(self, model_name, record_id, prompt_type="general"):
        """Get the prompt text and system prompt that will be used for AI analysis

        Args:
            model_name: The model name
            record_id: The record ID
            prompt_type: The type of prompt to use (general, follow_up, purchase_history, etc.)
        """
        _logger.info(
            f"Getting prompt info for {model_name} #{record_id} with prompt type {prompt_type}"
        )
        try:
            if not model_name or not record_id:
                return None

            # Check if this model is enabled for AI
            if not self._is_model_enabled_for_ai(model_name):
                return None

            # Get the AI settings for this model
            ai_settings = self._get_ai_settings(model_name)
            if not ai_settings:
                return None

            # Get the record
            record = self.env[model_name].browse(record_id)
            if not record.exists():
                return None

            # Get the data for the prompt
            data = {}
            if ai_settings.data_query:
                data = self.env["data.query.service"]._execute_data_query(
                    ai_settings.data_query, record_id
                )

            # Get the appropriate prompt template based on the prompt type
            prompt_template, system_prompt = self._get_prompt_template(
                model_name, prompt_type, ai_settings
            )

            # Replace placeholders in the prompt template
            prompt_text = self._replace_placeholders(prompt_template, record, data)

            # Add data query results to the prompt
            prompt_text += self.env["data.query.service"]._format_data_for_prompt(data)

            # Collect and add statistical data
            stats_data = self.env["statistics.service"]._collect_record_statistics(
                record, model_name
            )
            prompt_text += self.env["statistics.service"]._format_statistics_for_prompt(
                stats_data
            )

            return {
                "prompt_text": prompt_text,
                "system_prompt": system_prompt,
            }
        except Exception as e:
            _logger.error(f"Error getting prompt info: {str(e)}")
            return None
