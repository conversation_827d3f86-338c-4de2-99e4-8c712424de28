import logging

from odoo import api, models

_logger = logging.getLogger(__name__)


class DataQueryService(models.AbstractModel):
    _name = "data.query.service"
    _description = "Data Query Service"

    def _execute_data_query(self, query, record_id):
        """Execute a data query

        Args:
            query: The SQL query to execute
            record_id: The record ID to use in the query

        Returns:
            dict: The query result or empty dict
        """
        if not query:
            return {}

        try:
            self.env.cr.execute(query, {"res_id": record_id})
            return self.env.cr.dictfetchone() or {}
        except Exception as e:
            _logger.error(f"Error executing data query: {str(e)}")
            return {}

    def _format_data_for_prompt(self, data):
        """Format data query results for inclusion in a prompt

        Args:
            data: Dictionary of data query results

        Returns:
            str: Formatted data text for the prompt
        """
        if not data:
            return ""

        formatted_text = "\n\n--- DATA QUERY RESULTS ---\n"
        for key, value in data.items():
            formatted_text += f"\n{key}: {value}"
        formatted_text += "\n"

        return formatted_text

    def _get_default_query_for_model(self, model_name):
        """Get a default query for the given model"""
        # Default queries for common models
        default_queries = {
            "res.partner": """SELECT
                rp.name,
                rp.email,
                rp.phone,
                rp.create_date,
                COALESCE(
                    (SELECT COUNT(*) FROM sale_order
                    WHERE partner_id = rp.id AND state in ('sale', 'done'))
                , 0) as order_count,
                COALESCE(
                    (SELECT MAX(date_order) FROM sale_order
                    WHERE partner_id = rp.id AND state in ('sale', 'done'))
                , NULL) as last_order_date
            FROM
                res_partner rp
            WHERE
                rp.id = %(res_id)s""",
            "sale.order": """SELECT
                so.name,
                so.date_order,
                so.amount_total,
                so.amount_untaxed,
                so.amount_tax,
                rp.name as customer_name
            FROM
                sale_order so
            JOIN
                res_partner rp ON so.partner_id = rp.id
            WHERE
                so.id = %(res_id)s""",
            "product.product": """SELECT
                pp.name,
                pp.default_code,
                pt.list_price,
                pt.standard_price,
                (SELECT COUNT(*) FROM sale_order_line sol JOIN sale_order so ON sol.order_id = so.id
                WHERE sol.product_id = pp.id AND so.state in ('sale', 'done')) as times_sold
            FROM
                product_product pp
            JOIN
                product_template pt ON pp.product_tmpl_id = pt.id
            WHERE
                pp.id = %(res_id)s""",
        }

        # Return the default query for the model, or a generic query if not found
        if model_name in default_queries:
            return default_queries[model_name]
        else:
            table_name = model_name.replace(".", "_")
            return f"SELECT * FROM {table_name} WHERE id = %(res_id)s LIMIT 1"

    @api.model
    def save_query_result_to_chatter(
        self, model_name, record_id, result_text, title="Query Results"
    ):
        """Save query results to the chatter of a record"""
        _logger.info(f"Saving query result to chatter for {model_name} #{record_id}")
        try:
            if not model_name or not record_id:
                return False

            # Get the record
            record = self.env[model_name].browse(record_id)
            if not record.exists():
                return False

            # Format the message body
            body = f"<strong>{title}</strong><br/><pre>{result_text}</pre>"

            # Post the message
            record.message_post(
                body=body, message_type="comment", subtype_xmlid="mail.mt_note"
            )

            return True
        except Exception as e:
            _logger.error(f"Error saving query result to chatter: {str(e)}")
            return False
