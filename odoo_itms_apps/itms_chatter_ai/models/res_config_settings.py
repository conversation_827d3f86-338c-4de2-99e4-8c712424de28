from odoo import api, fields, models


class ResConfigSettings(models.TransientModel):
    _inherit = "res.config.settings"

    groq_default_model = fields.Selection(
        [
            ("deepseek-r1-distill-llama-70b-4k", "DeepSeek R1 Distill Llama 70B (4k)"),
            (
                "deepseek-r1-distill-llama-70b-32k",
                "DeepSeek R1 Distill Llama 70B (32k)",
            ),
            (
                "deepseek-r1-distill-llama-70b-high",
                "DeepSeek R1 Distill Llama 70B (>32k)",
            ),
            ("deepseek-r1-distill-qwen-32b", "DeepSeek R1 Distill Qwen 32B 128k"),
            ("qwen-2.5-32b-instruct", "Qwen 2.5 32B Instruct 128k"),
            ("qwen-2.5-coder-32b", "Qwen 2.5 Coder 32B Instruct 128k"),
            ("llama-3.2-1b-preview", "Llama 3.2 1B Preview 8k"),
            ("llama-3.2-3b-preview", "Llama 3.2 3B Preview 8k"),
            ("llama-3.3-70b-versatile", "Llama 3.3 70B Versatile 128k"),
            ("llama-3.1-8b-instant", "Llama 3.1 8B Instant 128k"),
            ("llama-3-70b", "Llama 3 70B 8k"),
            ("llama-3-8b", "Llama 3 8B 8k"),
            ("mixtral-8x7b-instruct", "Mixtral 8x7B Instruct 32k"),
            ("gemma-2-9b", "Gemma 2 9B 8k"),
            ("llama-guard-3-8b", "Llama Guard 3 8B 8k"),
            ("llama-3.3-70b-specdec", "Llama 3.3 70B SpecDec 8k"),
        ],
        string="Default Groq Model",
        default="llama-3.1-8b-instant",
    )

    ai_enabled_model_ids = fields.Many2many(
        "ir.model",
        "ai_config_model_rel",
        "config_id",
        "model_id",
        string="AI Enabled Models",
    )

    model_speed = fields.Float(
        compute="_compute_model_stats", string="Speed (Tokens/s)"
    )
    model_input_price = fields.Float(
        compute="_compute_model_stats", string="Input Price ($/M tokens)"
    )
    model_output_price = fields.Float(
        compute="_compute_model_stats", string="Output Price ($/M tokens)"
    )
    model_context_length = fields.Integer(
        compute="_compute_model_stats", string="Context Length"
    )

    @api.model
    def get_values(self):
        res = super().get_values()
        enabled_settings = self.env["ai.model.settings"].search(
            [("is_enabled", "=", True)]
        )
        res.update(
            {
                "groq_default_model": self.env["ir.config_parameter"]
                .sudo()
                .get_param("itms_chatter_ai.default_model", "llama-3.1-8b-instant"),
                "ai_enabled_model_ids": [
                    (6, 0, enabled_settings.mapped("model_id").ids)
                ],
            }
        )
        return res

    def set_values(self):
        super().set_values()
        self.env["ir.config_parameter"].set_param(
            "itms_chatter_ai.default_model",
            self.groq_default_model or "llama-3.1-8b-instant",
        )

        AISettings = self.env["ai.model.settings"]
        current_models = AISettings.search([("is_enabled", "=", True)]).mapped(
            "model_id"
        )

        # Enable new models
        for model in self.ai_enabled_model_ids - current_models:
            existing = AISettings.search([("model_id", "=", model.id)])
            if existing:
                existing.write({"is_enabled": True})
            else:
                AISettings.create({"model_id": model.id, "is_enabled": True})

        # Disable removed models
        for model in current_models - self.ai_enabled_model_ids:
            settings = AISettings.search([("model_id", "=", model.id)])
            if settings:
                settings.write({"is_enabled": False})

    @api.depends("groq_default_model")
    def _compute_model_stats(self):
        model_stats = {
            "deepseek-r1-distill-llama-70b-4k": {
                "speed": 275,
                "input_price": 0.75,
                "output_price": 0.99,
                "context": 4000,
            },
            "deepseek-r1-distill-llama-70b-32k": {
                "speed": 275,
                "input_price": 3.00,
                "output_price": 3.00,
                "context": 32000,
            },
            "deepseek-r1-distill-llama-70b-high": {
                "speed": 275,
                "input_price": 5.00,
                "output_price": 5.00,
                "context": 128000,
            },
            "deepseek-r1-distill-qwen-32b": {
                "speed": 140,
                "input_price": 0.69,
                "output_price": 0.69,
                "context": 128000,
            },
            "qwen-2.5-32b-instruct": {
                "speed": 200,
                "input_price": 0.79,
                "output_price": 0.79,
                "context": 128000,
            },
            "qwen-2.5-coder-32b": {
                "speed": 390,
                "input_price": 0.79,
                "output_price": 0.79,
                "context": 128000,
            },
            "llama-3.2-1b-preview": {
                "speed": 3100,
                "input_price": 0.04,
                "output_price": 0.04,
                "context": 8000,
            },
            "llama-3.2-3b-preview": {
                "speed": 1600,
                "input_price": 0.06,
                "output_price": 0.06,
                "context": 8000,
            },
            "llama-3.3-70b-versatile": {
                "speed": 275,
                "input_price": 0.59,
                "output_price": 0.79,
                "context": 128000,
            },
            "llama-3.1-8b-instant": {
                "speed": 750,
                "input_price": 0.05,
                "output_price": 0.08,
                "context": 128000,
            },
            "llama-3-70b": {
                "speed": 330,
                "input_price": 0.59,
                "output_price": 0.79,
                "context": 8000,
            },
            "llama-3-8b": {
                "speed": 1250,
                "input_price": 0.05,
                "output_price": 0.08,
                "context": 8000,
            },
            "mixtral-8x7b-instruct": {
                "speed": 575,
                "input_price": 0.24,
                "output_price": 0.24,
                "context": 32000,
            },
            "gemma-2-9b": {
                "speed": 500,
                "input_price": 0.20,
                "output_price": 0.20,
                "context": 8000,
            },
            "llama-guard-3-8b": {
                "speed": 765,
                "input_price": 0.20,
                "output_price": 0.20,
                "context": 8000,
            },
            "llama-3.3-70b-specdec": {
                "speed": 1600,
                "input_price": 0.59,
                "output_price": 0.99,
                "context": 8000,
            },
        }

        for record in self:
            stats = model_stats.get(
                record.groq_default_model,
                {"speed": 0.0, "input_price": 0.0, "output_price": 0.0, "context": 0},
            )
            record.model_speed = stats["speed"]
            record.model_input_price = stats["input_price"]
            record.model_output_price = stats["output_price"]
            record.model_context_length = stats["context"]

    @api.model
    def get_ai_enabled_models(self):
        """Get the technical names of models that have AI integration enabled"""
        settings = self.env["ai.model.settings"].search([("is_enabled", "=", True)])
        return settings.mapped("model_id.model")
