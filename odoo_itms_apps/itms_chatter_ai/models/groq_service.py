import logging

from odoo import api, models

_logger = logging.getLogger(__name__)


class GroqService(models.AbstractModel):
    _name = "groq.service"
    _description = "Groq AI Service"
    _inherit = ["groq.api.service"]

    def init(self):
        _logger.info("Initializing Groq Service Model")
        super().init()

    @api.model
    def get_ai_suggestions(self, model_name, record_id, prompt_type="general"):
        """Get AI suggestions for a record

        Args:
            model_name: The model name
            record_id: The record ID
            prompt_type: The type of prompt to use (general, follow_up, purchase_history, etc.)
        """
        _logger.info(
            f"Getting AI suggestions for {model_name} #{record_id} with prompt type {prompt_type}"
        )
        try:
            # Check if model is enabled
            if not self.env["prompt.service"]._is_model_enabled_for_ai(model_name):
                return f"Analysis is not enabled for {model_name}. Enable it in Settings > AI Integration."

            # Get record
            record = self.env[model_name].browse(record_id)
            if not record.exists():
                return "Record not found"

            # Get prompt info
            prompt_info = self.env["prompt.service"].get_prompt_info(
                model_name, record_id, prompt_type
            )
            if not prompt_info:
                return "Failed to generate prompt for analysis"

            # Get AI settings
            ai_settings = self.env["prompt.service"]._get_ai_settings(model_name)

            # Prepare prompt and system prompt
            prompt = prompt_info.get("prompt_text", "")
            system_prompt = prompt_info.get(
                "system_prompt", ai_settings.system_prompt or ""
            )

            # Prepare messages for API call
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})

            # Call the API
            return self._call_groq_api(messages)
        except Exception as e:
            _logger.error(f"Error getting AI suggestions: {str(e)}")
            return f"Error: {str(e)}"

    @api.model
    def get_ai_suggestions_with_custom_system_prompt(
        self, model_name, record_id, prompt_type="general", system_prompt=None
    ):
        """Get AI suggestions with a custom system prompt

        Args:
            model_name: The model name
            record_id: The record ID
            prompt_type: The type of prompt to use
            system_prompt: Custom system prompt to use

        Returns:
            str: AI-generated suggestions
        """
        _logger.info(
            f"Getting AI suggestions with custom system prompt for {model_name} #{record_id}"
        )
        try:
            # Check if model is enabled
            if not self.env["prompt.service"]._is_model_enabled_for_ai(model_name):
                return f"Analysis is not enabled for {model_name}. Enable it in Settings > AI Integration."

            # Get record
            record = self.env[model_name].browse(record_id)
            if not record.exists():
                return "Record not found"

            # Get prompt info
            prompt_info = self.env["prompt.service"].get_prompt_info(
                model_name, record_id, prompt_type
            )
            if not prompt_info:
                return "Failed to generate prompt for analysis"

            # Prepare prompt
            prompt = prompt_info.get("prompt_text", "")

            # Prepare messages for API call
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})

            # Call the API
            return self._call_groq_api(messages)
        except Exception as e:
            _logger.error(
                f"Error getting AI suggestions with custom system prompt: {str(e)}"
            )
            return f"Error: {str(e)}"

    # New method for search functionality
    @api.model
    def search_chatter_content(self, model, res_id, term):
        """Search across Chatter content for the given record"""
        results = []
        try:
            # Get the record
            record = self.env[model].browse(res_id)
            if not record.exists():
                return []

            # Search in messages
            domain = [
                ("model", "=", model),
                ("res_id", "=", res_id),
                "|",
                ("subject", "ilike", term),
                ("body", "ilike", term),
            ]

            messages = self.env["mail.message"].search(domain, limit=10)

            for message in messages:
                # Extract a snippet of text around the search term
                body = message.body or ""

                # Remove HTML tags for better searching
                from bs4 import BeautifulSoup

                soup = BeautifulSoup(body, "html.parser")
                text = soup.get_text()

                # Find the position of the term
                term_pos = text.lower().find(term.lower())
                if term_pos >= 0:
                    # Extract a snippet around the term
                    start = max(0, term_pos - 50)
                    end = min(len(text), term_pos + len(term) + 50)
                    snippet = text[start:end]

                    # Add ellipsis if we're not at the beginning/end
                    if start > 0:
                        snippet = "..." + snippet
                    if end < len(text):
                        snippet = snippet + "..."

                    results.append(
                        {
                            "id": message.id,
                            "date": message.date,
                            "author": message.author_id.name
                            if message.author_id
                            else "System",
                            "subject": message.subject or "No Subject",
                            "snippet": snippet,
                            "message_type": message.message_type,
                        }
                    )

            return results
        except Exception as e:
            _logger.error(f"Error searching chatter content: {str(e)}")
            return []
