from odoo import _, api, fields, models


class AIPromptTemplate(models.Model):
    _name = "ai.prompt.template"
    _description = "AI Prompt Template"
    _order = "sequence, id"

    name = fields.Char(string="Name", required=True)
    description = fields.Char(string="Description", required=True)
    template_key = fields.Char(
        string="Template Key",
        required=True,
        help="Technical key used to identify this template in the code",
    )
    sequence = fields.Integer(string="Sequence", default=10)
    model_id = fields.Many2one(
        "ir.model",
        string="Model",
        required=True,
        ondelete="cascade",
        help="The model this template applies to",
    )
    model_name = fields.Char(related="model_id.model", string="Model Name", store=True)
    prompt_template = fields.Text(
        string="Prompt Template",
        required=True,
        help="The template text with placeholders like ${field_name}",
    )
    system_prompt = fields.Text(
        string="System Prompt", help="Optional system prompt to use with this template"
    )
    is_default = fields.Boolean(
        string="Default Template",
        default=False,
        help="If checked, this will be the default template for this model",
    )
    is_enabled = fields.Boolean(string="Enabled", default=True)

    _sql_constraints = [
        (
            "unique_template_key_model",
            "unique(template_key, model_id)",
            "Template key must be unique per model!",
        )
    ]

    @api.model
    def create(self, vals):
        # If model_name is provided but model_id is not, try to find the model_id
        if vals.get("model_name") and not vals.get("model_id"):
            model = self.env["ir.model"].search(
                [("model", "=", vals["model_name"])], limit=1
            )
            if model:
                vals["model_id"] = model.id
            else:
                raise ValueError(f"Could not find model with name {vals['model_name']}")

        # If this is set as default, unset other defaults for this model
        if vals.get("is_default") and vals.get("model_id"):
            self.search(
                [("model_id", "=", vals["model_id"]), ("is_default", "=", True)]
            ).write({"is_default": False})

        return super(AIPromptTemplate, self).create(vals)

    def write(self, vals):
        # If this is set as default, unset other defaults for this model
        if vals.get("is_default"):
            for template in self:
                self.search(
                    [
                        ("model_id", "=", template.model_id.id),
                        ("id", "!=", template.id),
                        ("is_default", "=", True),
                    ]
                ).write({"is_default": False})
        return super(AIPromptTemplate, self).write(vals)

    @api.model
    def get_available_templates(self, model_name):
        """Get all available templates for a model"""
        return self.search(
            [("model_name", "=", model_name), ("is_enabled", "=", True)]
        ).read(["id", "name", "description", "template_key", "sequence"])

    @api.model
    def get_template(self, model_name, template_key=None):
        """Get a specific template or the default template for a model"""
        domain = [("model_name", "=", model_name), ("is_enabled", "=", True)]

        if template_key:
            domain.append(("template_key", "=", template_key))
            template = self.search(domain, limit=1)
            if template:
                return template

        # Fall back to default template
        domain.append(("is_default", "=", True))
        template = self.search(domain, limit=1)

        # If no default template, get the first available template
        if not template:
            template = self.search(
                [("model_name", "=", model_name), ("is_enabled", "=", True)], limit=1
            )

        return template

    def action_view_template(self):
        """Open the full template form view"""
        self.ensure_one()
        return {
            "type": "ir.actions.act_window",
            "name": "Edit Prompt Template",
            "res_model": "ai.prompt.template",
            "res_id": self.id,
            "view_mode": "form",
            "target": "current",
            "flags": {"mode": "edit"},
        }
