import logging

from odoo import _, api, fields, models

_logger = logging.getLogger(__name__)


class ClientAnalysis(models.Model):
    _name = "client.analysis"
    _description = "Client Analysis"
    _rec_name = "client_id"

    client_id = fields.Many2one(
        "res.partner", string="Client", required=True, ondelete="cascade"
    )
    total_annual_spend = fields.Float(string="Total Annual Spend", default=0.0)
    total_monthly_spend = fields.Float(string="Total Monthly Spend", default=0.0)
    last_analysis_date = fields.Date(string="Last Analysis Date")

    @api.model_create_multi
    def create(self, vals_list):
        records = super().create(vals_list)
        # Automatically refresh analysis after creation
        for record in records:
            record.refresh_analysis()
        return records

    def _get_annual_spend_query(self):
        return """
            SELECT COALESCE(SUM(amount_total), 0)
            FROM sale_order
            WHERE partner_id = %(partner_id)s
            AND state in ('sale', 'done')
            AND EXTRACT(YEAR FROM date_order) = EXTRACT(YEAR FROM CURRENT_DATE)
        """

    def _get_monthly_spend_query(self):
        return """
            SELECT COALESCE(SUM(amount_total), 0)
            FROM sale_order
            WHERE partner_id = %(partner_id)s
            AND state in ('sale', 'done')
            AND EXTRACT(YEAR FROM date_order) = EXTRACT(YEAR FROM CURRENT_DATE)
            AND EXTRACT(MONTH FROM date_order) = EXTRACT(MONTH FROM CURRENT_DATE)
        """

    def _execute_query(self, query, params):
        self.env.cr.execute(query, params)
        result = self.env.cr.fetchone()
        return result[0] if result else 0.0

    def refresh_analysis(self):
        for record in self:
            try:
                partner_id = record.client_id.id
                params = {"partner_id": partner_id}

                # Execute the queries and update the fields
                record.write(
                    {
                        "total_annual_spend": record._execute_query(
                            record._get_annual_spend_query(), params
                        ),
                        "total_monthly_spend": record._execute_query(
                            record._get_monthly_spend_query(), params
                        ),
                        "last_analysis_date": fields.Date.today(),
                    }
                )
                _logger.info(f"Successfully refreshed analysis for client {partner_id}")
            except Exception as e:
                _logger.error(
                    f"Error refreshing analysis for client {record.client_id.name}: {str(e)}"
                )
                raise


class ResPartner(models.Model):
    _inherit = "res.partner"

    client_analysis_id = fields.One2many(
        "client.analysis", "client_id", string="Client Analysis"
    )

    def ensure_client_analysis(self):
        """Ensure a client analysis record exists for this partner"""
        for partner in self:
            if not partner.client_analysis_id:
                self.env["client.analysis"].create({"client_id": partner.id})
