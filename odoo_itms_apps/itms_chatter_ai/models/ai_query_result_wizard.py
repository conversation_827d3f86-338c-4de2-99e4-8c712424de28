from odoo import api, fields, models


class AIQueryResultWizard(models.TransientModel):
    _name = "ai.query.result.wizard"
    _description = "AI Query Result Wizard"

    ai_settings_id = fields.Many2one(
        "ai.model.settings", string="AI Settings", required=True
    )
    title = fields.Char(string="Title", required=True, default="Query Results")
    record_id = fields.Integer(
        string="Record ID", help="ID of the record to save the results for"
    )

    def action_save(self):
        """Save the query results with the given title"""
        self.ensure_one()
        return self.ai_settings_id.save_query_results_with_title(
            self.title, self.record_id
        )
