import logging
from datetime import timedelta

from odoo import api, fields, models

_logger = logging.getLogger(__name__)


class StatisticsService(models.AbstractModel):
    _name = "statistics.service"
    _description = "Statistics Collection Service"

    def _collect_record_statistics(self, record, model_name):
        """Collect statistical data from a record

        Args:
            record: The record to collect statistics from
            model_name: The model name of the record

        Returns:
            dict: A dictionary of statistical data
        """
        stats_data = {}

        try:
            # Common statistical fields to include if they exist
            stat_fields = [
                # Financial metrics
                "total_annual_spend",
                "total_monthly_spend",
                "average_order_value",
                "amount_total",
                "amount_untaxed",
                "amount_tax",
                "margin",
                "profit",
                "list_price",
                "standard_price",
                "cost",
                "revenue",
                "price",
                "price_unit",
                "price_subtotal",
                "price_total",
                "discount",
                "discount_amount",
                "credit_limit",
                "debit",
                "credit",
                "balance",
                "total_invoiced",
                # Order/Sales metrics
                "order_count_ytd",
                "last_order_date",
                "total_orders",
                "lifetime_value",
                "sale_order_count",
                "purchase_order_count",
                "opportunity_count",
                "meeting_count",
                "task_count",
                "project_count",
                "ticket_count",
                "invoice_count",
                "payment_count",
                "delivery_count",
                "return_count",
                # Product metrics
                "qty_available",
                "virtual_available",
                "incoming_qty",
                "outgoing_qty",
                "sales_count",
                "purchase_count",
                "stock_value",
                "weight",
                "volume",
                # Time-related metrics
                "create_date",
                "write_date",
                "date_order",
                "date_invoice",
                "date_planned",
                "date_deadline",
                "date_start",
                "date_end",
                "last_activity_date",
                "last_stage_update",
                "last_update",
                # Status metrics
                "state",
                "status",
                "stage_id",
                "priority",
                "activity_state",
                "kanban_state",
                "active",
                "archived",
                "is_published",
                # Contact metrics
                "customer_rank",
                "supplier_rank",
                "employee_count",
                "industry_id",
                "country_id",
                "city",
                "zip",
                "phone",
                "email",
                "website",
                "lang",
                "tz",
                "user_id",
                "team_id",
                "company_id",
            ]

            # Add any available stats from the record
            for field in stat_fields:
                if hasattr(record, field) and record[field]:
                    # For many2one fields, get the name
                    if field.endswith("_id") and hasattr(record[field], "name"):
                        stats_data[field] = record[field].name
                    else:
                        stats_data[field] = record[field]

            # Add model-specific computed stats
            self._add_model_specific_stats(record, model_name, stats_data)

            # Add computed fields from the model
            self._add_computed_fields(record, model_name, stats_data)

            return stats_data
        except Exception as e:
            _logger.error(f"Error collecting record statistics: {str(e)}")
            return stats_data  # Return whatever we collected before the error

    def _add_model_specific_stats(self, record, model_name, stats_data):
        """Add model-specific statistics to the stats_data dictionary

        Args:
            record: The record to collect statistics from
            model_name: The model name of the record
            stats_data: The dictionary to add statistics to
        """
        try:
            if model_name == "res.partner":
                self._add_partner_stats(record, stats_data)
            elif model_name == "sale.order":
                self._add_sale_order_stats(record, stats_data)
            elif model_name == "product.product" or model_name == "product.template":
                self._add_product_stats(record, model_name, stats_data)
        except Exception as e:
            _logger.error(
                f"Error adding model-specific stats for {model_name}: {str(e)}"
            )

    def _add_partner_stats(self, record, stats_data):
        """Add partner-specific statistics"""
        try:
            # Try to get order count
            order_count = self.env["sale.order"].search_count(
                [("partner_id", "=", record.id)]
            )
            stats_data["order_count"] = order_count
        except Exception as e:
            _logger.debug(f"Error getting partner order count: {str(e)}")

        try:
            # Try to get invoice count
            invoice_count = self.env["account.move"].search_count(
                [("partner_id", "=", record.id), ("move_type", "=", "out_invoice")]
            )
            stats_data["invoice_count"] = invoice_count
        except Exception as e:
            _logger.debug(f"Error getting partner invoice count: {str(e)}")

        try:
            # Try to get total amount invoiced
            invoices = self.env["account.move"].search(
                [
                    ("partner_id", "=", record.id),
                    ("move_type", "=", "out_invoice"),
                    ("state", "=", "posted"),
                ]
            )
            total_invoiced = sum(inv.amount_total for inv in invoices)
            stats_data["total_amount_invoiced"] = total_invoiced
        except Exception as e:
            _logger.debug(f"Error getting partner total invoiced: {str(e)}")

    def _add_sale_order_stats(self, record, stats_data):
        """Add sale order-specific statistics"""
        try:
            # Try to get delivery status
            if hasattr(record, "picking_ids"):
                delivery_status = "No deliveries"
                if record.picking_ids:
                    all_done = all(p.state == "done" for p in record.picking_ids)
                    if all_done:
                        delivery_status = "All delivered"
                    else:
                        delivery_status = "Partially delivered"
                stats_data["delivery_status"] = delivery_status
        except Exception as e:
            _logger.debug(f"Error getting sale order delivery status: {str(e)}")

    def _add_product_stats(self, record, model_name, stats_data):
        """Add product-specific statistics"""
        try:
            product_id = record.id
            if model_name == "product.template" and record.product_variant_ids:
                product_id = record.product_variant_ids[0].id

            if product_id:
                sales_last_30_days = self.env["sale.order.line"].search_count(
                    [
                        ("product_id", "=", product_id),
                        (
                            "order_id.date_order",
                            ">=",
                            fields.Datetime.now() - timedelta(days=30),
                        ),
                    ]
                )
                stats_data["sales_last_30_days"] = sales_last_30_days
        except Exception as e:
            _logger.debug(f"Error getting product sales stats: {str(e)}")

    def _add_computed_fields(self, record, model_name, stats_data):
        """Add computed fields from the model to the stats_data dictionary"""
        try:
            model_obj = self.env[model_name]
            for field_name, field in model_obj._fields.items():
                if (
                    field.compute
                    and hasattr(record, field_name)
                    and field_name not in stats_data
                ):
                    try:
                        value = record[field_name]
                        if value:
                            # For many2one fields, get the name
                            if field.type == "many2one" and hasattr(value, "name"):
                                stats_data[field_name] = value.name
                            # For many2many and one2many fields, get the count
                            elif field.type in ["many2many", "one2many"]:
                                stats_data[f"{field_name}_count"] = len(value)
                            # For other fields, get the value directly
                            elif field.type in [
                                "char",
                                "text",
                                "integer",
                                "float",
                                "monetary",
                                "boolean",
                                "date",
                                "datetime",
                            ]:
                                stats_data[field_name] = value
                    except Exception:
                        # Skip fields that raise exceptions when accessed
                        pass
        except Exception as e:
            _logger.debug(f"Error adding computed fields for {model_name}: {str(e)}")

    def _format_statistics_for_prompt(self, stats_data):
        """Format statistical data for inclusion in a prompt

        Args:
            stats_data: Dictionary of statistical data

        Returns:
            str: Formatted statistics text for the prompt
        """
        if not stats_data:
            return ""

        formatted_text = "\n--- STATISTICAL DATA ---\n"
        for key, value in stats_data.items():
            # Format dates nicely
            if hasattr(value, "strftime"):
                value = value.strftime("%Y-%m-%d")
            # Format monetary values
            elif isinstance(value, (int, float)) and any(
                key.endswith(suffix)
                for suffix in [
                    "_price",
                    "_spend",
                    "_value",
                    "amount_",
                    "margin",
                    "profit",
                ]
            ):
                value = f"{value:.2f}"
            formatted_text += f"\n{key}: {value}"
        formatted_text += "\n\n"

        return formatted_text
