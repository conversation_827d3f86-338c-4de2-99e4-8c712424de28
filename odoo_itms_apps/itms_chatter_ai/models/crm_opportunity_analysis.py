import logging
from datetime import date, datetime

from dateutil.relativedelta import relativedelta
from odoo import _, api, fields, models

_logger = logging.getLogger(__name__)


class CrmLead(models.Model):
    _inherit = "crm.lead"

    # Analysis metrics
    sales_cycle_duration = fields.Integer(
        string="Sales Cycle Duration (Days)",
        compute="_compute_opportunity_metrics",
        store=True,
        help="Number of days since the opportunity was created",
    )
    activity_count_total = fields.Integer(
        string="Total Activities",
        compute="_compute_opportunity_metrics",
        store=True,
        help="Total number of activities for this opportunity",
    )
    last_activity_date = fields.Date(
        string="Last Activity Date",
        compute="_compute_opportunity_metrics",
        store=True,
        help="Date of the most recent activity",
    )
    next_action_date = fields.Date(
        string="Next Action Date",
        compute="_compute_opportunity_metrics",
        store=True,
        help="Date of the next scheduled activity",
    )
    probability_trend = fields.Float(
        string="Probability Trend (%)",
        compute="_compute_opportunity_metrics",
        store=True,
        help="Trend in probability over time",
    )
    last_analysis_date = fields.Date(
        string="Last Analysis Date", compute="_compute_opportunity_metrics", store=True
    )

    @api.depends(
        "create_date", "activity_ids", "activity_ids.date_deadline", "probability"
    )
    def _compute_opportunity_metrics(self):
        _logger.info("========== Computing CRM Opportunity metrics ==========")
        today = date.today()

        for lead in self:
            _logger.info(
                f"Computing opportunity metrics for lead {lead.id} ({lead.name})"
            )

            # Sales cycle duration
            if lead.create_date:
                create_date = lead.create_date.date()
                lead.sales_cycle_duration = (today - create_date).days
            else:
                lead.sales_cycle_duration = 0

            # Activity metrics
            activities = self.env["mail.activity"].search(
                [("res_id", "=", lead.id), ("res_model", "=", "crm.lead")]
            )

            # Total activity count
            lead.activity_count_total = len(activities)

            # Last activity date
            last_activity = activities.sorted(
                key=lambda r: r.date_deadline, reverse=True
            )
            lead.last_activity_date = (
                last_activity[0].date_deadline if last_activity else False
            )

            # Next action date
            next_action = activities.filtered(lambda a: a.date_deadline >= today)
            next_action = next_action.sorted(key=lambda r: r.date_deadline)
            lead.next_action_date = (
                next_action[0].date_deadline if next_action else False
            )

            # Probability trend (in a real implementation, you might track history)
            lead.probability_trend = lead.probability

            # Update last analysis date
            lead.last_analysis_date = today

            _logger.info(
                f"Metrics for {lead.name}: Sales Cycle={lead.sales_cycle_duration}, Activities={lead.activity_count_total}, Last Activity={lead.last_activity_date}, Next Action={lead.next_action_date}"
            )

    def refresh_opportunity_analysis(self):
        """Force refresh of opportunity metrics"""
        _logger.info(
            f"========== Manually refreshing analysis for lead {self.id} =========="
        )
        self._compute_opportunity_metrics()
        return {"type": "ir.actions.client", "tag": "reload"}
