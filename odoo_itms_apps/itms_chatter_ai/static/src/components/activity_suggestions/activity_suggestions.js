/** @odoo-module **/

import { Component, useState, onWillStart } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";
import { NotificationService } from "@web/core/notifications/notification_service";
import { registry } from "@web/core/registry";

export class ActivitySuggestions extends Component {
    setup() {
        // Bind methods to preserve 'this' context
        this.loadSuggestions = this.loadSuggestions.bind(this);
        this.acceptSuggestion = this.acceptSuggestion.bind(this);
        this.acceptAllSuggestions = this.acceptAllSuggestions.bind(this);
        this.rejectSuggestion = this.rejectSuggestion.bind(this);
        this.rejectAllSuggestions = this.rejectAllSuggestions.bind(this);
        this.getPriorityClass = this.getPriorityClass.bind(this);
        this.getActivityTypeIcon = this.getActivityTypeIcon.bind(this);
        this.state = useState({
            suggestions: [],
            loading: false,
            error: null,
            success: null,
            showSuggestions: false,
        });

        // Initialize services
        this.orm = useService("orm");

        try {
            // Try to get the notification service
            this.notification = useService("notification");
        } catch (error) {
            console.warn("Notification service not available, using fallback");
            // Fallback notification implementation
            this.notification = {
                add: (message, options) => {
                    console.log(`Notification: ${message}`, options);
                    // Show an alert for critical errors
                    if (options && options.type === "danger") {
                        alert(message);
                    }
                }
            };
        }

        onWillStart(async () => {
            if (this.props.autoLoad) {
                await this.loadSuggestions();
            }
        });
    }

    async loadSuggestions() {
        this.state.loading = true;
        this.state.error = null;
        this.state.success = null;

        console.log("Loading suggestions for", this.props.resModel, this.props.resId, this.props.promptType || "follow_up");

        try {
            const result = await this.orm.call(
                "activity.suggestion.service",
                "get_ai_suggestions_as_json",
                [this.props.resModel, this.props.resId, this.props.promptType || "follow_up"]
            );

            console.log("Suggestions result:", result);

            if (result.success) {
                this.state.suggestions = result.suggestions;
                this.state.showSuggestions = true;
                console.log("Loaded suggestions:", this.state.suggestions);
            } else {
                this.state.error = result.error || "Failed to get activity suggestions";
                console.error("Failed to get suggestions:", result.error);
            }
        } catch (error) {
            console.error("Error loading suggestions:", error);
            this.state.error = error.message || "An unexpected error occurred";
        } finally {
            this.state.loading = false;
        }
    }

    async acceptSuggestion(suggestion, index) {
        try {
            console.log("Accepting suggestion:", suggestion);

            // Show a temporary message
            this.state.success = "Creating activity...";

            const result = await this.orm.call(
                "activity.suggestion.service",
                "create_activity_from_suggestion",
                [this.props.resModel, this.props.resId, suggestion]
            );

            console.log("Activity creation result:", result);

            if (result && result.success) {
                // Remove the suggestion from the list
                this.state.suggestions.splice(index, 1);

                // Show success message
                this.state.success = result.message || "Activity created successfully";
                if (this.notification) {
                    this.notification.add(this.state.success, { type: "success" });
                }

                // If no more suggestions, hide the component
                if (this.state.suggestions.length === 0) {
                    this.state.showSuggestions = false;
                    this.state.success = "All suggestions have been processed";
                }
            } else {
                // Show error message
                const errorMsg = (result && result.error) || "Failed to create activity";
                this.state.error = errorMsg;
                if (this.notification) {
                    this.notification.add(errorMsg, { type: "danger" });
                }
                console.error("Failed to create activity:", errorMsg);
            }
        } catch (error) {
            console.error("Error accepting suggestion:", error);
            this.state.error = error.message || "An unexpected error occurred";
            if (this.notification) {
                this.notification.add(this.state.error, { type: "danger" });
            }
        }
    }

    async acceptAllSuggestions() {
        try {
            console.log("Accepting all suggestions:", this.state.suggestions);

            // Show a temporary message
            this.state.success = "Creating activities...";

            const result = await this.orm.call(
                "activity.suggestion.service",
                "create_activities_from_suggestions",
                [this.props.resModel, this.props.resId, this.state.suggestions]
            );

            console.log("All activities creation result:", result);

            if (result && result.success) {
                this.state.suggestions = [];
                this.state.showSuggestions = false;
                this.state.success = `Created ${result.created_count} out of ${result.total_count} activities`;
                if (this.notification) {
                    this.notification.add(this.state.success, { type: "success" });
                }
            } else {
                const errorMsg = (result && result.error) || "Failed to create activities";
                this.state.error = errorMsg;
                if (this.notification) {
                    this.notification.add(errorMsg, { type: "danger" });
                }
                console.error("Failed to create activities:", errorMsg);
            }
        } catch (error) {
            console.error("Error accepting all suggestions:", error);
            this.state.error = error.message || "An unexpected error occurred";
            if (this.notification) {
                this.notification.add(this.state.error, { type: "danger" });
            }
        }
    }

    rejectSuggestion(index) {
        // Simply remove the suggestion from the list
        this.state.suggestions.splice(index, 1);

        // If no more suggestions, hide the component
        if (this.state.suggestions.length === 0) {
            this.state.showSuggestions = false;
            this.state.success = "All suggestions have been processed";
        }
    }

    rejectAllSuggestions() {
        this.state.suggestions = [];
        this.state.showSuggestions = false;
        this.state.success = "All suggestions have been rejected";
    }

    getPriorityClass(priority) {
        switch (priority) {
            case "high":
                return "text-danger";
            case "medium":
                return "text-warning";
            case "low":
                return "text-success";
            default:
                return "";
        }
    }

    getActivityTypeIcon(activityType) {
        switch (activityType) {
            case "call":
                return "fa-phone";
            case "email":
                return "fa-envelope";
            case "meeting":
                return "fa-calendar";
            case "todo":
                return "fa-check-square";
            default:
                return "fa-tasks";
        }
    }
}

ActivitySuggestions.template = "itms_chatter_ai.ActivitySuggestions";
ActivitySuggestions.props = {
    resModel: String,
    resId: Number,
    promptType: { type: String, optional: true },
    autoLoad: { type: Boolean, optional: true },
};
