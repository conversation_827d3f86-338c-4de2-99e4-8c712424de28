<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="itms_chatter_ai.ActivitySuggestions" owl="1">
        <div class="o_activity_suggestions">
            <div t-if="state.loading" class="text-center p-3">
                <i class="fa fa-spinner fa-spin fa-2x"></i>
                <p>Generating activity suggestions...</p>
            </div>

            <div t-if="state.error" class="alert alert-danger" role="alert">
                <i class="fa fa-exclamation-triangle me-2"></i>
                <span t-esc="state.error"></span>
            </div>

            <div t-if="state.success" class="alert alert-success" role="alert">
                <i class="fa fa-check-circle me-2"></i>
                <span t-esc="state.success"></span>
            </div>

            <div t-if="!state.loading and !state.error and !state.success and !state.showSuggestions" class="text-center p-3">
                <button class="btn btn-primary" t-on-click="() => this.loadSuggestions()">
                    <i class="fa fa-magic me-2"></i>Generate Activity Suggestions
                </button>
            </div>

            <div t-if="state.showSuggestions" class="o_activity_suggestions_list">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">AI-Generated Activity Suggestions</h5>
                    <div>
                        <button class="btn btn-success me-2" t-on-click="() => this.acceptAllSuggestions()">
                            <i class="fa fa-check me-1"></i>Accept All
                        </button>
                        <button class="btn btn-danger" t-on-click="() => this.rejectAllSuggestions()">
                            <i class="fa fa-times me-1"></i>Reject All
                        </button>
                    </div>
                </div>

                <div class="list-group">
                    <div t-foreach="state.suggestions" t-as="suggestion" t-key="suggestion_index"
                         class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between align-items-center">
                            <div>
                                <div class="d-flex align-items-center">
                                    <i t-attf-class="fa {{ getActivityTypeIcon(suggestion.activity_type) }} me-2"></i>
                                    <h5 class="mb-1" t-esc="suggestion.summary"></h5>
                                </div>
                                <p class="mb-1" t-esc="suggestion.note"></p>
                                <small>
                                    <span t-attf-class="badge {{ getPriorityClass(suggestion.priority) }} me-2">
                                        <t t-esc="suggestion.priority.charAt(0).toUpperCase() + suggestion.priority.slice(1)"></t> Priority
                                    </span>
                                    <span class="text-muted">
                                        Due in <t t-esc="suggestion.days_from_now"></t> days
                                    </span>
                                </small>
                            </div>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-success"
                                        t-on-click="() => this.acceptSuggestion(suggestion, suggestion_index)">
                                    <i class="fa fa-check"></i>
                                </button>
                                <button class="btn btn-sm btn-danger"
                                        t-on-click="() => this.rejectSuggestion(suggestion_index)">
                                    <i class="fa fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>
</templates>
