/* @odoo-module */

import { Component, useState, onMounted, useRef } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";
import { ActivitySuggestions } from "../activity_suggestions/activity_suggestions";


/**
 * Client Analysis Component
 *
 * Displays analysis data for clients in the chatter
 */
export class ClientAnalysis extends Component {
    static template = "itms_chatter_ai.ClientAnalysis";
    static props = {
        thread: { type: Object },
    };

    setup() {
        this.state = useState({
            loading: true,
            error: null,
            record: null,
            aiAnalysis: null,
            aiAnalysisTimestamp: null,
            isAnalyzing: false,
            isCollapsed: false,
            dataQuery: null,
            dataQueryCollapsed: true,
            queryResult: null,
            queryResultCollapsed: true,
            promptText: null,
            systemPrompt: null,
            systemPromptCollapsed: true,
            promptCollapsed: false,
            activeTab: 'results',
            promptType: 'general',
            availablePromptTypes: [],
        });

        this.orm = useService("orm");
        this.notification = useService("notification");
        this.action = useService("action");
        this.chartRef = useRef("chart");
        this.chartInstance = null;

        onMounted(async () => {
            await this.loadAnalysisData();
            await this.loadPromptTypes();
            this.renderChartWhenReady();

            // Try to load saved analysis from localStorage
            try {
                const storageKey = `odoo_ai_analysis_${this.props.thread.model}_${this.props.thread.id}`;
                const savedAnalysis = localStorage.getItem(storageKey);
                if (savedAnalysis) {
                    const parsed = JSON.parse(savedAnalysis);
                    this.state.aiAnalysis = parsed.analysis;
                    this.state.aiAnalysisTimestamp = parsed.timestamp;
                    // Load collapsed states if available
                    if (parsed.isCollapsed !== undefined) {
                        this.state.isCollapsed = parsed.isCollapsed;
                    }
                    if (parsed.dataQueryCollapsed !== undefined) {
                        this.state.dataQueryCollapsed = parsed.dataQueryCollapsed;
                    }
                    if (parsed.queryResultCollapsed !== undefined) {
                        this.state.queryResultCollapsed = parsed.queryResultCollapsed;
                    }
                    if (parsed.systemPromptCollapsed !== undefined) {
                        this.state.systemPromptCollapsed = parsed.systemPromptCollapsed;
                    }
                    if (parsed.promptCollapsed !== undefined) {
                        this.state.promptCollapsed = parsed.promptCollapsed;
                    }
                    if (parsed.promptType !== undefined) {
                        this.state.promptType = parsed.promptType;
                    }

                    // Load prompt text and system prompt if available
                    if (parsed.promptText) {
                        this.state.promptText = parsed.promptText;
                    }
                    if (parsed.systemPrompt) {
                        this.state.systemPrompt = parsed.systemPrompt;
                    }
                }
            } catch (e) {
                console.warn("Could not load saved analysis from localStorage", e);
            }
        });
    }

    async loadAnalysisData() {
        if (!this.props.thread?.model || !this.props.thread?.id) {
            this.state.error = "Invalid thread data";
            this.state.loading = false;
            return;
        }

        try {
            this.state.loading = true;

            // Load different data based on model
            if (this.props.thread.model === 'res.partner') {
                // Set default values for partners
                this.state.record = {
                    total_annual_spend: 0,
                    total_monthly_spend: 0,
                    average_order_value: 0,
                    order_count_ytd: 0,
                    last_order_date: null,
                };

                // Try to load partner data
                const fields = [
                    'total_annual_spend',
                    'total_monthly_spend',
                    'average_order_value',
                    'order_count_ytd',
                    'last_order_date',
                    'currency_id',
                    'name',
                    'create_date',
                    'email',
                    'phone',
                    'mobile',
                    'street',
                    'city',
                    'country_id'
                ];

                const result = await this.orm.read(
                    this.props.thread.model,
                    [this.props.thread.id],
                    fields
                );

                // Only override default values if we get valid results
                if (result && result.length > 0) {
                    const data = result[0];
                    // Safely update with valid data, falling back to defaults for any missing fields
                    this.state.record = {
                        total_annual_spend: data.total_annual_spend || 0,
                        total_monthly_spend: data.total_monthly_spend || 0,
                        average_order_value: data.average_order_value || 0,
                        order_count_ytd: data.order_count_ytd || 0,
                        last_order_date: data.last_order_date || null,
                        currency_id: data.currency_id || null,
                    };
                }
            }
            else if (this.props.thread.model === 'crm.lead') {
                // For CRM leads, we'll load the data directly from the query results
                // Set default empty record for now
                this.state.record = {};
            }
            else {
                // Default empty record for any other model
                this.state.record = {};
            }

            // Load the data query from AI settings
            await this.loadDataQuery();

        } catch (error) {
            console.error("Error loading analysis data:", error);
            this.state.error = error.message || "Failed to load data";
        } finally {
            this.state.loading = false;
        }
    }

    async loadPromptTypes() {
        try {
            // Load prompt types based on model
            if (this.props.thread.model === 'res.partner' || this.props.thread.model === 'crm.lead') {
                // Get available prompt templates from the backend
                const templates = await this.orm.call(
                    "ai.prompt.template",
                    "get_available_templates",
                    [this.props.thread.model]
                );

                if (templates && templates.length > 0) {
                    // Sort templates by sequence
                    templates.sort((a, b) => a.sequence - b.sequence);

                    // Map to the format expected by the UI
                    this.state.availablePromptTypes = templates.map(template => ({
                        id: template.template_key,
                        name: template.name,
                        description: template.description
                    }));
                } else {
                    // Fallback to default prompt types if none are defined
                    if (this.props.thread.model === 'res.partner') {
                        this.state.availablePromptTypes = [
                            { id: 'general', name: 'General Analysis', description: 'Overall analysis of the contact' },
                            { id: 'follow_up', name: 'Follow Up', description: 'Recommendations for follow-up actions' },
                        ];
                    } else {
                        this.state.availablePromptTypes = [
                            { id: 'crm_general', name: 'Opportunity Analysis', description: 'Overall analysis of the opportunity' },
                            { id: 'crm_follow_up', name: 'Follow-up Activities', description: 'Recommended follow-up activities' },
                        ];
                    }
                }
            } else {
                // For other models, just use a general analysis type
                this.state.availablePromptTypes = [
                    { id: 'general', name: 'General Analysis', description: 'Overall analysis' },
                ];
            }
        } catch (error) {
            console.error("Error loading prompt types:", error);
            // Fallback to a basic prompt type
            this.state.availablePromptTypes = [
                { id: 'general', name: 'General Analysis', description: 'Overall analysis' },
            ];
        }
    }

    async loadDataQuery() {
        try {
            console.log('Loading data query for model:', this.props.thread.model);

            // Find the AI settings for this model - don't filter by is_enabled to ensure we get the settings
            const aiSettings = await this.orm.call(
                "ai.model.settings",
                "search_read",
                [[['model_name', '=', this.props.thread.model]], ['data_query', 'is_enabled'], 0, 1, false]
            );

            console.log('AI Settings found:', aiSettings);

            // Set a default query based on the model
            let defaultQuery = this._getDefaultQueryForModel(this.props.thread.model);

            if (aiSettings && aiSettings.length > 0 && aiSettings[0].data_query) {
                // Use the configured query if available
                this.state.dataQuery = aiSettings[0].data_query;
                console.log('Data Query loaded from settings:', this.state.dataQuery);
            } else {
                // Use the default query
                this.state.dataQuery = defaultQuery;
                console.log('Using default query for', this.props.thread.model);
            }

            // Always fetch the results if we have a query
            if (this.state.dataQuery) {
                await this.loadQueryResults();
            } else {
                console.warn('No query available for model:', this.props.thread.model);
            }
        } catch (error) {
            console.error("Error loading data query:", error);
            // Set a default query as fallback
            this.state.dataQuery = this._getDefaultQueryForModel(this.props.thread.model);
            await this.loadQueryResults();
        }
    }

    _getDefaultQueryForModel(modelName) {
        // Default queries for common models
        const defaultQueries = {
            'res.partner': `SELECT
    rp.name,
    rp.email,
    rp.phone,
    rp.create_date,
    COALESCE(
        (SELECT COUNT(*) FROM sale_order
         WHERE partner_id = rp.id AND state in ('sale', 'done'))
    , 0) as order_count,
    COALESCE(
        (SELECT MAX(date_order) FROM sale_order
         WHERE partner_id = rp.id AND state in ('sale', 'done'))
    , NULL) as last_order_date
FROM
    res_partner rp
WHERE
    rp.id = %(res_id)s`,

            'sale.order': `SELECT
    so.name,
    so.date_order,
    so.amount_total,
    so.amount_untaxed,
    so.amount_tax,
    rp.name as customer_name
FROM
    sale_order so
JOIN
    res_partner rp ON so.partner_id = rp.id
WHERE
    so.id = %(res_id)s`,

            'product.product': `SELECT
    pp.name,
    pp.default_code,
    pt.list_price,
    pt.standard_price,
    (SELECT COUNT(*) FROM sale_order_line sol JOIN sale_order so ON sol.order_id = so.id
     WHERE sol.product_id = pp.id AND so.state in ('sale', 'done')) as times_sold
FROM
    product_product pp
JOIN
    product_template pt ON pp.product_tmpl_id = pt.id
WHERE
    pp.id = %(res_id)s`
        };

        // Return the default query for the model, or a generic query if not found
        return defaultQueries[modelName] || `SELECT * FROM ${modelName.replace('.', '_')} WHERE id = %(res_id)s LIMIT 1`;
    }

    async loadQueryResults() {
        try {
            console.log('Executing query for model:', this.props.thread.model, 'record ID:', this.props.thread.id);

            // Call the backend to execute the query and get results
            const result = await this.orm.call(
                "groq.service",
                "execute_data_query",
                [this.props.thread.model, this.props.thread.id]
            );

            console.log('Query result:', result);

            if (result) {
                this.state.queryResult = result;
                console.log('Query result stored in state');
            } else {
                console.warn('Query returned null or empty result');

                // For testing purposes, create a mock result if we're on res.partner
                if (this.props.thread.model === 'res.partner') {
                    this.state.queryResult = {
                        name: 'Test Partner',
                        email: '<EMAIL>',
                        phone: '******-123-4567',
                        create_date: new Date().toISOString(),
                        order_count: 5,
                        last_order_date: new Date().toISOString()
                    };
                    console.log('Using mock data for testing');
                }
            }
        } catch (error) {
            console.error("Error executing data query:", error);
            // Don't set an error state, just log it - this is a non-critical feature

            // For testing purposes, create a mock result if we're on res.partner
            if (this.props.thread.model === 'res.partner') {
                this.state.queryResult = {
                    name: 'Test Partner',
                    email: '<EMAIL>',
                    phone: '******-123-4567',
                    create_date: new Date().toISOString(),
                    order_count: 5,
                    last_order_date: new Date().toISOString()
                };
                console.log('Using mock data after error');
            }
        }
    }

    async saveQueryResultToChatter() {
        try {
            // First, find the AI settings for this model
            const aiSettings = await this.orm.call(
                "ai.model.settings",
                "search_read",
                [[['model_name', '=', this.props.thread.model]], ['id'], 0, 1, false]
            );

            if (!aiSettings || aiSettings.length === 0) {
                this.notification.add("No AI settings found for this model", {
                    type: "warning",
                    sticky: false,
                });
                return;
            }

            // Open the wizard to get the title
            // First, check if the query result exists
            if (!this.state.queryResult) {
                this.notification.add("No query results to save", {
                    type: "warning",
                    sticky: false,
                });
                return;
            }

            // Format the query result as a string
            let resultText = '';
            try {
                // Create a table-like format for the query result
                const keys = Object.keys(this.state.queryResult);
                const values = Object.values(this.state.queryResult);

                // Add header
                resultText += "Query Results:\n\n";

                // Add each key-value pair
                for (let i = 0; i < keys.length; i++) {
                    const key = keys[i];
                    let value = values[i];

                    // Format the value based on its type
                    if (value === null || value === undefined) {
                        value = 'NULL';
                    } else if (typeof value === 'object') {
                        value = JSON.stringify(value);
                    }

                    resultText += `${key}: ${value}\n`;
                }
            } catch (e) {
                resultText = JSON.stringify(this.state.queryResult, null, 2);
            }

            // Save directly to the chatter using a server action
            await this.orm.call(
                'groq.service',  // Use our service model
                'save_query_result_to_chatter',  // Create this method
                [
                    this.props.thread.model,
                    this.props.thread.id,
                    resultText,
                    'Query Results'
                ]
            );

            this.notification.add("Query results saved to chatter", {
                type: "success",
                sticky: false,
            });
        } catch (error) {
            console.error("Error saving query result to chatter:", error);
            this.notification.add("Error saving query result: " + error.message, {
                type: "danger",
                sticky: false,
            });
        }
    }

    renderChartWhenReady() {
        const attemptRender = () => {
            if (!this.chartRef.el) {
                setTimeout(attemptRender, 100); // Retry every 100ms
                return;
            }
            this.renderChart();
        };
        attemptRender();
    }

    renderChart() {
        // Only render chart for res.partner model
        if (this.props.thread.model !== 'res.partner' || !this.chartRef.el || typeof ApexCharts === 'undefined') {
            return;
        }

        try {
            if (this.chartInstance) {
                this.chartInstance.destroy();
            }

            const options = {
                chart: {
                    type: 'bar',
                    height: 250,
                },
                series: [{
                    name: 'Amount',
                    data: [
                        this.state.record.total_annual_spend || 0,
                        this.state.record.total_monthly_spend || 0,
                        this.state.record.average_order_value || 0,
                    ],
                }],
                xaxis: {
                    categories: ['Annual Spend', 'Monthly Spend', 'Avg Order Value'],
                },
                yaxis: {
                    labels: {
                        formatter: (val) => `$${val.toLocaleString('en-US')}`,
                    },
                },
                colors: ['#007bff'],
                dataLabels: {
                    enabled: true,
                    formatter: (val) => '$' + val.toLocaleString('en-US'),
                },
            };

            this.chartInstance = new ApexCharts(this.chartRef.el, options);
            this.chartInstance.render();
        } catch (error) {
            console.error("Chart rendering failed:", error);
        }
    }

    formatCurrency(value) {
        if (!value && value !== 0) return '$0.00';
        return `$ ${parseFloat(value).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        })}`;
    }

    formatDate(dateStr) {
        if (!dateStr) return 'N/A';
        const date = new Date(dateStr);
        return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
    }

    copyToMessage() {
        if (!this.state.aiAnalysis) return;

        try {
            // Activate the message tab
            this.notification.add("AI Analysis copied. Switching to message tab...", {
                type: "success",
                sticky: false,
            });

            // Activate the message tab using DOM selection
            setTimeout(() => {
                // Switch to the message tab
                const messageTab = document.querySelector('a[href="#updates"]');
                if (messageTab) {
                    messageTab.click();
                }

                // Click the send message button if it's not already clicked
                setTimeout(() => {
                    const sendMessageBtn = document.querySelector('.o-mail-Chatter-sendMessage:not(.active)');
                    if (sendMessageBtn) {
                        sendMessageBtn.click();
                    }

                    // Set the content in the editor after a short delay
                    setTimeout(() => {
                        // Copy to clipboard as a fallback
                        navigator.clipboard.writeText(this.state.aiAnalysis);

                        // Try to find and update the composer
                        let attemptCount = 0;
                        const injectContent = () => {
                            // First try HTML editor
                            const richEditor = document.querySelector('.o_field_html[contenteditable=true], .note-editable');
                            if (richEditor) {
                                richEditor.innerHTML = this.state.aiAnalysis.replace(/\n/g, '<br>');
                                const event = new Event('input', { bubbles: true });
                                richEditor.dispatchEvent(event);
                                return true;
                            }

                            // Then try plain textarea
                            const textEditor = document.querySelector('textarea.o_composer_text_field');
                            if (textEditor) {
                                textEditor.value = this.state.aiAnalysis;
                                const event = new Event('input', { bubbles: true });
                                textEditor.dispatchEvent(event);
                                return true;
                            }

                            // Check the CKEditor if present
                            const ckEditor = document.querySelector('.ck-editor__editable');
                            if (ckEditor) {
                                // If using CKEditor (some Odoo versions use it)
                                ckEditor.innerHTML = this.state.aiAnalysis.replace(/\n/g, '<br>');
                                const event = new Event('input', { bubbles: true });
                                ckEditor.dispatchEvent(event);
                                return true;
                            }

                            return false;
                        };

                        const checkEditor = () => {
                            if (injectContent()) return;

                            // If we couldn't find the editor yet, try again after a short delay (max 10 attempts)
                            if (attemptCount < 10) {
                                setTimeout(checkEditor, 200);
                                attemptCount++;
                            }
                        };

                        checkEditor();
                    }, 300);
                }, 200);
            }, 100);
        } catch (error) {
            console.error("Error copying to message:", error);
            // Fallback to clipboard
            navigator.clipboard.writeText(this.state.aiAnalysis);
            this.notification.add("Analysis copied to clipboard. Paste it manually.", {
                type: "warning",
                sticky: false,
            });
        }
    }

    toggleCollapse() {
        this.state.isCollapsed = !this.state.isCollapsed;

        // Store the collapsed state in localStorage
        try {
            const storageKey = `odoo_ai_analysis_${this.props.thread.model}_${this.props.thread.id}`;
            const savedData = localStorage.getItem(storageKey);
            if (savedData) {
                const parsed = JSON.parse(savedData);
                parsed.isCollapsed = this.state.isCollapsed;
                localStorage.setItem(storageKey, JSON.stringify(parsed));
            }
        } catch (e) {
            console.warn("Could not save collapsed state to localStorage", e);
        }
    }

    toggleDataQueryCollapse() {
        this.state.dataQueryCollapsed = !this.state.dataQueryCollapsed;

        // Store the data query collapsed state in localStorage
        try {
            const storageKey = `odoo_ai_analysis_${this.props.thread.model}_${this.props.thread.id}`;
            const savedData = localStorage.getItem(storageKey);
            if (savedData) {
                const parsed = JSON.parse(savedData);
                parsed.dataQueryCollapsed = this.state.dataQueryCollapsed;
                localStorage.setItem(storageKey, JSON.stringify(parsed));
            }
        } catch (e) {
            console.warn("Could not save data query collapsed state to localStorage", e);
        }
    }

    toggleQueryResultCollapse() {
        this.state.queryResultCollapsed = !this.state.queryResultCollapsed;

        // Store the query result collapsed state in localStorage
        try {
            const storageKey = `odoo_ai_analysis_${this.props.thread.model}_${this.props.thread.id}`;
            const savedData = localStorage.getItem(storageKey);
            if (savedData) {
                const parsed = JSON.parse(savedData);
                parsed.queryResultCollapsed = this.state.queryResultCollapsed;
                localStorage.setItem(storageKey, JSON.stringify(parsed));
            }
        } catch (e) {
            console.warn("Could not save query result collapsed state to localStorage", e);
        }
    }

    toggleSystemPrompt() {
        this.state.systemPromptCollapsed = !this.state.systemPromptCollapsed;

        // Store the system prompt collapsed state in localStorage
        try {
            const storageKey = `odoo_ai_analysis_${this.props.thread.model}_${this.props.thread.id}`;
            const savedData = localStorage.getItem(storageKey);
            if (savedData) {
                const parsed = JSON.parse(savedData);
                parsed.systemPromptCollapsed = this.state.systemPromptCollapsed;
                localStorage.setItem(storageKey, JSON.stringify(parsed));
            }
        } catch (e) {
            console.warn("Could not save system prompt collapsed state to localStorage", e);
        }
    }

    togglePromptCollapse() {
        this.state.promptCollapsed = !this.state.promptCollapsed;

        // Store the prompt collapsed state in localStorage
        try {
            const storageKey = `odoo_ai_analysis_${this.props.thread.model}_${this.props.thread.id}`;
            const savedData = localStorage.getItem(storageKey);
            if (savedData) {
                const parsed = JSON.parse(savedData);
                parsed.promptCollapsed = this.state.promptCollapsed;
                localStorage.setItem(storageKey, JSON.stringify(parsed));
            }
        } catch (e) {
            console.warn("Could not save prompt collapsed state to localStorage", e);
        }
    }

    setActiveTab(tabName) {
        this.state.activeTab = tabName;
    }

    setPromptType(promptType) {
        this.state.promptType = promptType;

        // Store the prompt type in localStorage
        try {
            const storageKey = `odoo_ai_analysis_${this.props.thread.model}_${this.props.thread.id}`;
            const savedData = localStorage.getItem(storageKey);
            if (savedData) {
                const parsed = JSON.parse(savedData);
                parsed.promptType = this.state.promptType;
                localStorage.setItem(storageKey, JSON.stringify(parsed));
            }
        } catch (e) {
            console.warn("Could not save prompt type to localStorage", e);
        }
    }

    removeAnalysis() {
        // Clear the analysis from state
        this.state.aiAnalysis = null;
        this.state.aiAnalysisTimestamp = null;

        // Remove from localStorage
        try {
            const storageKey = `odoo_ai_analysis_${this.props.thread.model}_${this.props.thread.id}`;
            localStorage.removeItem(storageKey);

            this.notification.add("AI Analysis removed", {
                type: "success",
                sticky: false,
            });
        } catch (e) {
            console.warn("Could not remove analysis from localStorage", e);
        }
    }

    async onAnalyzeClick() {
        if (this.state.isAnalyzing) return;

        try {
            this.state.isAnalyzing = true;
            this.state.error = null; // Clear any previous errors

            // First, get the prompt that will be used
            try {
                const promptInfo = await this.orm.call(
                    "groq.service",
                    "get_prompt_info",
                    [this.props.thread.model, this.props.thread.id, this.state.promptType]
                );

                if (promptInfo) {
                    this.state.promptText = promptInfo.prompt_text || null;
                    this.state.systemPrompt = promptInfo.system_prompt || null;
                }
            } catch (promptError) {
                console.error("Error getting prompt info:", promptError);
                // Continue anyway to get the analysis
            }

            // Call the backend service for AI analysis
            const result = await this.orm.call(
                "groq.service",
                "get_ai_suggestions",
                [this.props.thread.model, this.props.thread.id, this.state.promptType]
            );

            // Check if we got a valid result (handle server error messages)
            if (typeof result === 'string' && result.startsWith('Error:')) {
                this.state.error = result;
            } else {
                // Update state with results
                this.state.aiAnalysis = result;
                this.state.aiAnalysisTimestamp = new Date().toLocaleString();
                this.state.isCollapsed = false; // Ensure it's expanded when new analysis is generated

                // Persist the analysis to localStorage
                try {
                    const storageKey = `odoo_ai_analysis_${this.props.thread.model}_${this.props.thread.id}`;
                    localStorage.setItem(storageKey, JSON.stringify({
                        analysis: result,
                        timestamp: this.state.aiAnalysisTimestamp,
                        isCollapsed: false,
                        dataQueryCollapsed: this.state.dataQueryCollapsed,
                        queryResultCollapsed: this.state.queryResultCollapsed,
                        systemPromptCollapsed: this.state.systemPromptCollapsed,
                        promptCollapsed: this.state.promptCollapsed,
                        promptType: this.state.promptType,
                        promptText: this.state.promptText,
                        systemPrompt: this.state.systemPrompt,
                    }));
                } catch (e) {
                    console.warn("Could not persist analysis to localStorage", e);
                }
            }

        } catch (error) {
            console.error("Error during AI analysis:", error);
            this.state.error = error.message || "Failed to perform AI analysis";
        } finally {
            this.state.isAnalyzing = false;
        }
    }
}

// Register the ActivitySuggestions component
ClientAnalysis.components = { ActivitySuggestions };
