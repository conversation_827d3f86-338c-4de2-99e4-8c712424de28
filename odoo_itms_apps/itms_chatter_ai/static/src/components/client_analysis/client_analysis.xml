<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="itms_chatter_ai.ClientAnalysis">
        <div class="client-analysis-container p-3">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="m-0">Analysis</h5>
                <div class="d-flex align-items-center">
                    <div class="dropdown me-2" t-if="props.thread.model === 'res.partner' || props.thread.model === 'crm.lead'">
                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <t t-foreach="state.availablePromptTypes" t-as="promptType" t-key="promptType.id">
                                <t t-if="promptType.id === state.promptType">
                                    <i class="fa fa-lightbulb me-1"/> <t t-esc="promptType.name"/>
                                </t>
                            </t>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <t t-foreach="state.availablePromptTypes" t-as="promptType" t-key="promptType.id">
                                <li>
                                    <a class="dropdown-item" href="#" t-on-click.prevent="() => this.setPromptType(promptType.id)">
                                        <t t-if="promptType.id === state.promptType">
                                            <i class="fa fa-check text-success me-1"/>
                                        </t>
                                        <t t-else="">
                                            <i class="fa fa-lightbulb me-1 text-muted"/>
                                        </t>
                                        <span><t t-esc="promptType.name"/></span>
                                        <small class="d-block text-muted"><t t-esc="promptType.description"/></small>
                                    </a>
                                </li>
                            </t>
                        </ul>
                    </div>
                    <button t-on-click="onAnalyzeClick" t-att-disabled="state.isAnalyzing" class="btn btn-primary btn-sm">
                        <t t-if="state.isAnalyzing">
                            <i class="fa fa-spinner fa-spin me-1"/> Analyzing...
                        </t>
                        <t t-else="">
                            <i class="fa fa-chart-bar me-1"/> Run Analysis
                        </t>
                    </button>
                </div>
            </div>

            <!-- Loading State -->
            <t t-if="state.loading">
                <div class="text-center p-3">
                    <i class="fa fa-spinner fa-spin fa-2x text-primary"></i>
                    <p class="mt-2 text-muted">Loading analysis data...</p>
                </div>
            </t>
            <t t-elif="state.error">
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle me-2"/>
                    <span t-esc="state.error"/>
                </div>
                <!-- Still render chart with fallback data -->
                <div class="mt-5">
                    <h6 class="mb-3">Monthly Spending Trend</h6>
                    <div t-ref="chart" class="chart-container" style="min-height: 250px;"></div>
                </div>
            </t>
            <t t-else="">
                <!-- Sub-tabs for Analysis Content -->
                <ul class="nav nav-tabs mb-3" id="analysisSubTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview-tab-pane"
                                type="button" role="tab" aria-controls="overview-tab-pane" aria-selected="true">
                            <i class="fa fa-chart-line me-1"></i> Overview
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="data-tab" data-bs-toggle="tab" data-bs-target="#data-tab-pane"
                                type="button" role="tab" aria-controls="data-tab-pane" aria-selected="false">
                            <i class="fa fa-database me-1"></i> Data
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="results-tab" data-bs-toggle="tab" data-bs-target="#results-tab-pane"
                                type="button" role="tab" aria-controls="results-tab-pane" aria-selected="false">
                            <i class="fa fa-robot me-1"></i> Results
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="analysisTabContent">
                    <!-- Overview Tab -->
                    <div class="tab-pane fade show active" id="overview-tab-pane" role="tabpanel" aria-labelledby="overview-tab" tabindex="0">
                        <!-- Statistical Data -->
                        <t t-if="props.thread.model === 'res.partner'">
                            <!-- Partner Overview -->
                            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                                <div class="col">
                                    <div class="card h-100 shadow-sm border-0">
                                        <div class="card-body">
                                            <h6 class="card-title text-muted">Total Annual Spend</h6>
                                            <p class="card-text fs-4 fw-bold text-primary" t-esc="formatCurrency(state.record.total_annual_spend)"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card h-100 shadow-sm border-0">
                                        <div class="card-body">
                                            <h6 class="card-title text-muted">Total Monthly Spend</h6>
                                            <p class="card-text fs-4 fw-bold text-primary" t-esc="formatCurrency(state.record.total_monthly_spend)"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card h-100 shadow-sm border-0">
                                        <div class="card-body">
                                            <h6 class="card-title text-muted">Average Order Value</h6>
                                            <p class="card-text fs-4 fw-bold text-primary" t-esc="formatCurrency(state.record.average_order_value)"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card h-100 shadow-sm border-0">
                                        <div class="card-body">
                                            <h6 class="card-title text-muted">Orders This Year</h6>
                                            <p class="card-text fs-4 fw-bold text-primary" t-esc="state.record.order_count_ytd or 0"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card h-100 shadow-sm border-0">
                                        <div class="card-body">
                                            <h6 class="card-title text-muted">Last Order Date</h6>
                                            <p class="card-text fs-4 fw-bold text-primary" t-esc="formatDate(state.record.last_order_date)"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card h-100 shadow-sm border-0">
                                        <div class="card-body">
                                            <h6 class="card-title text-muted">Customer Lifetime Value</h6>
                                            <p class="card-text fs-4 fw-bold text-success" t-esc="formatCurrency(state.record.lifetime_value || (state.record.total_annual_spend ? state.record.total_annual_spend * 3 : 0))"/>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Chart Container for Partner -->
                            <div class="mt-5">
                                <h6 class="mb-3">Monthly Spending Trend</h6>
                                <div t-ref="chart" class="chart-container" style="min-height: 250px;"></div>
                            </div>
                        </t>
                        <t t-elif="props.thread.model === 'crm.lead'">
                            <!-- CRM Lead/Opportunity Overview -->
                            <div class="alert alert-info mb-4">
                                <i class="fa fa-info-circle me-2"></i>
                                <span>Opportunity Analysis provides AI-powered insights to help you close deals more effectively.</span>
                            </div>
                            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                                <div class="col">
                                    <div class="card h-100 shadow-sm border-0">
                                        <div class="card-body">
                                            <h6 class="card-title text-muted">Expected Revenue</h6>
                                            <p class="card-text fs-4 fw-bold text-primary" t-esc="formatCurrency(state.queryResult?.expected_revenue || 0)"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card h-100 shadow-sm border-0">
                                        <div class="card-body">
                                            <h6 class="card-title text-muted">Probability</h6>
                                            <p class="card-text fs-4 fw-bold text-primary">
                                                <t t-esc="state.queryResult?.probability || 0"/>%
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card h-100 shadow-sm border-0">
                                        <div class="card-body">
                                            <h6 class="card-title text-muted">Stage</h6>
                                            <p class="card-text fs-4 fw-bold text-primary" t-esc="state.queryResult?.stage_name || 'Not Set'"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card h-100 shadow-sm border-0">
                                        <div class="card-body">
                                            <h6 class="card-title text-muted">Type</h6>
                                            <p class="card-text fs-4 fw-bold text-primary" t-esc="state.queryResult?.type || 'Unknown'"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card h-100 shadow-sm border-0">
                                        <div class="card-body">
                                            <h6 class="card-title text-muted">Activity Count</h6>
                                            <p class="card-text fs-4 fw-bold text-primary" t-esc="state.queryResult?.activity_count || 0"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="card h-100 shadow-sm border-0">
                                        <div class="card-body">
                                            <h6 class="card-title text-muted">Next Activity Date</h6>
                                            <p class="card-text fs-4 fw-bold text-primary" t-esc="formatDate(state.queryResult?.next_activity_date)"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                        <t t-else="">
                            <!-- Generic Overview for other models -->
                            <div class="alert alert-info">
                                <i class="fa fa-info-circle me-2"></i>
                                <span>Run the analysis to see AI-powered insights for this record.</span>
                            </div>
                        </t>
                    </div>

                    <!-- Results Tab -->
                    <div class="tab-pane fade" id="results-tab-pane" role="tabpanel" aria-labelledby="results-tab" tabindex="0">
                        <div class="row">
                            <!-- Prompt Section (Top) -->
                            <div class="col-12 mb-4">
                                <div class="card shadow-sm border-0 bg-light">
                                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                        <h6 class="m-0">Prompt Sent to AI</h6>
                                        <button t-on-click="togglePromptCollapse" class="btn btn-sm btn-outline-secondary">
                                            <i t-if="state.promptCollapsed" class="fa fa-expand me-1"/>
                                            <i t-else="" class="fa fa-compress me-1"/>
                                            <t t-if="state.promptCollapsed">Show Prompt</t>
                                            <t t-else="">Hide Prompt</t>
                                        </button>
                                    </div>
                                    <div t-if="!state.promptCollapsed" class="card-body" style="max-height: 300px; overflow-y: auto; border-bottom: 1px solid rgba(0,0,0,0.1);">
                                        <div t-if="state.promptText" class="prompt-container bg-light p-3 rounded border">
                                            <pre class="prompt-text text-muted mb-0" style="white-space: pre-wrap; font-family: monospace; font-size: 0.9rem;"><t t-esc="state.promptText"/></pre>
                                        </div>
                                        <div t-else="" class="alert alert-info mb-0">
                                            <i class="fa fa-info-circle me-2"></i>
                                            No prompt available. Run an analysis to see the prompt sent to the AI.
                                        </div>
                                    </div>
                                    <div t-if="state.systemPrompt &amp;&amp; !state.promptCollapsed" class="card-footer bg-light">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <h6 class="m-0 text-muted">System Prompt</h6>
                                            <button t-on-click="toggleSystemPrompt" class="btn btn-sm btn-outline-secondary">
                                                <i t-if="state.systemPromptCollapsed" class="fa fa-eye me-1"/>
                                                <i t-else="" class="fa fa-eye-slash me-1"/>
                                                <t t-if="state.systemPromptCollapsed">Show</t>
                                                <t t-else="">Hide</t>
                                            </button>
                                        </div>
                                        <div t-if="!state.systemPromptCollapsed" class="system-prompt-container bg-light p-3 rounded border mt-2">
                                            <pre class="system-prompt-text text-muted mb-0" style="white-space: pre-wrap; font-family: monospace; font-size: 0.9rem;"><t t-esc="state.systemPrompt"/></pre>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Analysis Results Section (Bottom) -->
                            <div class="col-12">
                                <!-- AI Analysis Results -->
                                <div t-if="state.aiAnalysis" class="mb-4">
                                    <div class="card shadow-sm border-0">
                                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                            <h6 class="m-0">Analysis Results</h6>
                                            <small t-if="state.aiAnalysisTimestamp" class="text-muted">Generated: <span t-esc="state.aiAnalysisTimestamp"/></small>
                                        </div>
                                        <div class="card-body" style="max-height: 400px; overflow-y: auto; border-bottom: 1px solid rgba(0,0,0,0.1);">
                                            <p t-attf-class="ai-analysis-text {{ state.isCollapsed ? 'collapsed' : '' }}" t-esc="state.aiAnalysis"></p>
                                        </div>
                                        <!-- Activity Suggestions Component -->
                                        <div t-if="state.promptType === 'follow_up'" class="mt-4 mb-3">
                                            <h6 class="mb-3"><i class="fa fa-tasks me-2"></i>Suggested Follow-up Activities</h6>
                                            <ActivitySuggestions
                                                resModel="props.thread.model"
                                                resId="props.thread.id"
                                                promptType="'follow_up'"
                                            />
                                        </div>

                                        <div class="card-footer bg-white text-end" style="position: sticky; bottom: 0; z-index: 1; box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);">
                                            <button t-on-click="removeAnalysis" class="btn btn-sm btn-danger me-2">
                                                <i class="fa fa-trash me-1"/> Remove
                                            </button>
                                            <button t-on-click="toggleCollapse" class="btn btn-sm btn-info me-2">
                                                <i t-if="state.isCollapsed" class="fa fa-expand me-1"/>
                                                <i t-else="" class="fa fa-compress me-1"/>
                                                <t t-if="state.isCollapsed">Expand</t>
                                                <t t-else="">Collapse</t>
                                            </button>
                                            <button t-on-click="copyToMessage" class="btn btn-sm btn-secondary">
                                                <i class="fa fa-copy me-1"/> Copy to Message
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div t-else="" class="alert alert-info">
                                    <i class="fa fa-info-circle me-2"></i>
                                    No analysis results available. Click "Run Analysis" to generate insights.
                                </div>
                            </div>
                        </div>
                    </div>

                <!-- Data Tab -->
                <div class="tab-pane fade" id="data-tab-pane" role="tabpanel" aria-labelledby="data-tab" tabindex="0">
                    <!-- Data Query Card -->
                    <div class="mb-4">
                        <div class="card shadow-sm border-0">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h6 class="m-0">Data Query Configuration</h6>
                                <button t-on-click="toggleDataQueryCollapse" class="btn btn-sm btn-outline-secondary">
                                    <i t-if="state.dataQueryCollapsed" class="fa fa-code me-1"/>
                                    <i t-else="" class="fa fa-compress me-1"/>
                                    <t t-if="state.dataQueryCollapsed">Show Query</t>
                                    <t t-else="">Hide Query</t>
                                </button>
                            </div>
                            <div t-if="!state.dataQueryCollapsed" class="card-body">
                                <t t-if="state.dataQuery">
                                    <pre class="data-query-text bg-light p-3 rounded"><code t-esc="state.dataQuery"></code></pre>
                                </t>
                                <t t-else="">
                                    <div class="alert alert-info mb-0">
                                        <i class="fa fa-info-circle me-2"></i>
                                        No data query configured for this model. Configure it in AI Model Settings.
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>

                    <!-- Data Query Result Card -->
                    <div class="mb-4">
                        <div class="card shadow-sm border-0">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h6 class="m-0">Data Query Result</h6>
                                <div>
                                    <button t-on-click="saveQueryResultToChatter" class="btn btn-sm btn-info me-2">
                                        <i class="fa fa-save me-1"/> Save to Chatter
                                    </button>
                                    <button t-on-click="toggleQueryResultCollapse" class="btn btn-sm btn-outline-secondary">
                                        <i t-if="state.queryResultCollapsed" class="fa fa-table me-1"/>
                                        <i t-else="" class="fa fa-compress me-1"/>
                                        <t t-if="state.queryResultCollapsed">Show Result</t>
                                        <t t-else="">Hide Result</t>
                                    </button>
                                </div>
                            </div>
                            <div t-if="!state.queryResultCollapsed" class="card-body">
                                <t t-if="state.queryResult">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th t-foreach="Object.keys(state.queryResult)" t-as="key" t-key="key" scope="col">
                                                        <t t-esc="key"/>
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td t-foreach="Object.values(state.queryResult)" t-as="value" t-key="value_index">
                                                        <t t-if="value === null || value === undefined">
                                                            <span class="text-muted">NULL</span>
                                                        </t>
                                                        <t t-elif="typeof value === 'object' &amp;&amp; value !== null">
                                                            <pre class="mb-0"><code t-esc="JSON.stringify(value, null, 2)"/></pre>
                                                        </t>
                                                        <t t-else="">
                                                            <t t-esc="value"/>
                                                        </t>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </t>
                                <t t-elif="state.dataQuery">
                                    <div class="alert alert-info mb-0">
                                        <i class="fa fa-info-circle me-2"></i>
                                        No results available. The query may not have returned any data.
                                    </div>
                                </t>
                                <t t-else="">
                                    <div class="alert alert-info mb-0">
                                        <i class="fa fa-info-circle me-2"></i>
                                        Loading query results...
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>


                </div>
            </t>
        </div>
    </t>
</templates>
