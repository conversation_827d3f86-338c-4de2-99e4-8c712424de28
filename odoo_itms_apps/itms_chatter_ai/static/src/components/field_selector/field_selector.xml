<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="itms_chatter_ai.FieldSelector">
        <div class="field-selector">
            <!-- Loading state -->
            <div t-if="state.loading" class="text-center p-3">
                <i class="fa fa-spinner fa-spin"/> Loading fields...
            </div>

            <!-- Field selector -->
            <div t-else="" class="field-selector-content">
                <!-- Search input -->
                <div class="input-group mb-3">
                    <input type="text" class="form-control"
                           t-att-placeholder="props.placeholder ? 'Search ' + props.placeholder : 'Search fields...'"
                           t-on-input="onSearchInput"
                           t-att-value="state.search"
                           t-att-readonly="props.readonly"/>
                    <div class="input-group-append">
                        <span class="input-group-text">
                            <i class="fa fa-search"/>
                        </span>
                    </div>
                </div>

                <!-- Selected fields -->
                <div t-if="state.selectedFields.length > 0" class="selected-fields mb-3">
                    <label>Selected Fields:</label>
                    <div class="d-flex flex-wrap">
                        <t t-foreach="state.selectedFields" t-as="fieldName" t-key="fieldName">
                            <span class="badge badge-primary m-1">
                                <t t-esc="fieldName"/>
                                <i t-if="!props.readonly" class="fa fa-times ml-1" t-on-click="() => this.onRemoveField(fieldName)"/>
                            </span>
                        </t>
                    </div>
                </div>

                <!-- Available fields -->
                <div class="available-fields">
                    <label>Available Fields:</label>
                    <div class="field-list">
                        <t t-if="filteredFields.length === 0">
                            <div class="text-center text-muted">
                                No fields matching your search.
                            </div>
                        </t>
                        <div t-else="" class="list-group">
                            <t t-foreach="filteredFields" t-as="field" t-key="field.id">
                                <a href="#" class="list-group-item list-group-item-action"
                                   t-att-class="{'disabled': props.readonly}"
                                   t-on-click.prevent="!props.readonly ? (() => this.onAddField(field)) : undefined">
                                    <strong t-esc="field.name"/>
                                    <span class="text-muted ml-2">(<t t-esc="field.ttype"/>)</span>
                                    <div class="text-muted small" t-esc="field.field_description"/>
                                </a>
                            </t>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>
</templates>
