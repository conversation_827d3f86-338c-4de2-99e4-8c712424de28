/** @odoo-module **/

import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";
import { Component, useState, onMounted } from "@odoo/owl";

/**
 * Field Selector Widget
 *
 * A widget to select fields from a model with auto-suggestions
 */
export class FieldSelector extends Component {
    static template = "itms_chatter_ai.FieldSelector";
    static props = {
        record: { type: Object },
        name: { type: String },
        readonly: { type: Boolean, optional: true },
        id: { type: String, optional: true },
        placeholder: { type: String, optional: true },
    };

    setup() {
        this.state = useState({
            fields: [],
            loading: true,
            selectedFields: [],
            search: "",
        });

        this.orm = useService("orm");

        onMounted(async () => {
            await this.loadFields();
            this.loadSelectedFields();
        });
    }

    async loadFields() {
        try {
            this.state.loading = true;
            const modelId = this.props.record.data.model_id[0];
            if (!modelId) return;

            const fields = await this.orm.call(
                "ir.model.fields",
                "search_read",
                [
                    [["model_id", "=", modelId]],
                    ["name", "field_description", "ttype"]
                ]
            );

            this.state.fields = fields;
        } catch (error) {
            console.error("Error loading fields:", error);
        } finally {
            this.state.loading = false;
        }
    }

    loadSelectedFields() {
        try {
            const value = this.props.record.data[this.props.name];
            if (value) {
                this.state.selectedFields = JSON.parse(value);
            }
        } catch (error) {
            console.error("Error parsing selected fields:", error);
            this.state.selectedFields = [];
        }
    }

    get filteredFields() {
        if (!this.state.search) return this.state.fields;
        const search = this.state.search.toLowerCase();
        return this.state.fields.filter(
            field => field.name.toLowerCase().includes(search) ||
                    field.field_description.toLowerCase().includes(search)
        );
    }

    onAddField(field) {
        if (!this.state.selectedFields.includes(field.name)) {
            const selectedFields = [...this.state.selectedFields, field.name];
            this.state.selectedFields = selectedFields;
            this.updateValue(selectedFields);
        }
    }

    onRemoveField(fieldName) {
        const selectedFields = this.state.selectedFields.filter(name => name !== fieldName);
        this.state.selectedFields = selectedFields;
        this.updateValue(selectedFields);
    }

    onSearchInput(ev) {
        this.state.search = ev.target.value;
    }

    updateValue(selectedFields) {
        this.props.record.update({[this.props.name]: JSON.stringify(selectedFields)});
    }
}

// Register Field Selector Widget
registry.category("fields").add("field_selector", {
    component: FieldSelector,
});
