/** @odoo-module **/

import { Chatter } from "@mail/chatter/web_portal/chatter";
import { patch } from "@web/core/utils/patch";
import { ClientAnalysis } from "@itms_chatter_ai/components/client_analysis/client_analysis";
import { useService } from "@web/core/utils/hooks";
import { useState, onWillStart } from "@odoo/owl";

/**
 * Add the ClientAnalysis component to the Chatter
 */
const chatterSetup = Chatter.prototype.setup;
patch(Chatter.prototype, {
    setup() {
        chatterSetup.call(this);
        this.orm = useService("orm");

        // State to track if AI is enabled for this model
        this.aiState = useState({
            isAIEnabled: false,
            enabledModels: [],
            loading: true
        });

        onWillStart(async () => {
            await this._loadAIEnabledModels();
        });
    },

    /**
     * Load the list of models that have AI enabled
     */
    async _loadAIEnabledModels() {
        try {
            // Get the list of enabled models from the backend by checking AI settings
            const aiSettings = await this.orm.search_read(
                "ai.model.settings",
                [["is_enabled", "=", true]],
                ["model_name"]
            );

            // Extract model names from the settings
            const enabledModels = aiSettings.map(setting => setting.model_name);

            this.aiState.enabledModels = enabledModels || [];
            this.aiState.isAIEnabled = this.aiState.enabledModels.includes(this.props.threadModel);
            console.log(`AI enabled for ${this.props.threadModel}: ${this.aiState.isAIEnabled}`);
        } catch (error) {
            console.error("Error loading AI enabled models:", error);
            // Default to showing the tab to avoid blocking the user
            this.aiState.isAIEnabled = true;
        } finally {
            this.aiState.loading = false;
        }
    },

    /**
     * Check if the AI Analysis tab should be shown
     * Only show for res.partner and crm.lead models
     */
    get showAIAnalysisTab() {
        return !this.aiState.loading &&
               this.aiState.isAIEnabled &&
               (this.props.threadModel === 'res.partner' || this.props.threadModel === 'crm.lead');
    }
});

// Add the ClientAnalysis component to the Chatter components
patch(Chatter, {
    components: {
        ...Chatter.components,
        ClientAnalysis,
    },
});
