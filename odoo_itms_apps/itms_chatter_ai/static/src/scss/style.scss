/* AI Analysis Tab Styles */

.ai-analysis-text {
    white-space: pre-line;
    line-height: 1.6;
    font-size: 0.95rem;
    transition: max-height 0.3s ease-in-out, overflow 0.3s ease-in-out;
    overflow: visible;
    max-height: 1000px; /* Adjust based on your needs */
    position: relative;

    &.collapsed {
        max-height: 4.8em; /* Show approximately 3 lines */
        overflow: hidden;

        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2em;
            background: linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
            pointer-events: none;
        }
    }
}

.ai-analysis-text ul, .ai-analysis-text ol {
    padding-left: 1.5rem;
    margin-bottom: 1rem;
}

.client-analysis-container h5 {
    color: #555;
}

.card-header small.text-muted {
    font-size: 0.8rem;
    font-style: italic;
}

.tab-pane#ai-analysis .card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;

    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
    }
}

.tab-pane#ai-analysis .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Data Query Styles */
.data-query-text {
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 0.85rem;
    line-height: 1.5;
    white-space: pre-wrap;
    word-break: break-word;
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 0;
    border: 1px solid #e9ecef;
    color: #0366d6;
    background-color: #f6f8fa !important;
}

/* Add a subtle animation to the data query card */
.card:has(.data-query-text) {
    transition: box-shadow 0.3s ease-in-out;
}

.card:has(.data-query-text):hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
