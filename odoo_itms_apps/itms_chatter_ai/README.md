# ITMS Chatter AI - Odoo 18

This module adds AI features to the Odoo chatter, including an AI Analysis tab for enabled models.

## Working Features

1. **AI Model Settings**: Configure which models should have AI integration enabled
2. **AI Analysis Tab**: Display an AI Analysis tab in the chatter for enabled models
3. **Groq Integration**: Configure and select from various Groq models

## Installation

1. Install the module
2. Go to Settings → AI Integration and select models to enable
3. Configure the default Groq model in Settings → Chatter AI

## Technical Notes

- Uses Odoo 18's new view syntax
- Uses simplified JS for maximum stability
- Only models enabled in settings will show the AI tab

## Troubleshooting

If you encounter issues:
1. Clear browser cache
2. Restart Odoo server
3. Check browser console for errors
4. Check Odoo logs for errors
