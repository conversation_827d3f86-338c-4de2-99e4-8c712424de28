<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form_firebase" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.firebase</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="inside">
                <app data-string="Firebase Push Notification"
                     string="Firebase Push Notification"
                     name="firebase_push_notification"
                     groups="base.group_system">
                    <block title="Firebase Configuration">
                        <setting string="Firebase Credentials" help="Enter your Firebase credentials for push notifications">
                            <div class="content-group">
                                <div class="row mt16">
                                    <label for="firebase_type" class="col-lg-3 o_light_label"/>
                                    <field name="firebase_type" placeholder="service_account"/>
                                </div>
                                <div class="row mt16">
                                    <label for="firebase_project_id" class="col-lg-3 o_light_label"/>
                                    <field name="firebase_project_id" placeholder="Your Firebase Project ID"/>
                                </div>
                                <div class="row mt16">
                                    <label for="firebase_private_key_id" class="col-lg-3 o_light_label"/>
                                    <field name="firebase_private_key_id" placeholder="Private Key ID"/>
                                </div>
                                <div class="row mt16">
                                    <label for="firebase_private_key" class="col-lg-3 o_light_label"/>
                                    <field name="firebase_private_key" placeholder="-----BEGIN PRIVATE KEY-----..." widget="text"/>
                                </div>
                                <div class="row mt16">
                                    <label for="firebase_client_email" class="col-lg-3 o_light_label"/>
                                    <field name="firebase_client_email" placeholder="<EMAIL>"/>
                                </div>
                                <div class="row mt16">
                                    <label for="firebase_client_id" class="col-lg-3 o_light_label"/>
                                    <field name="firebase_client_id" placeholder="Client ID"/>
                                </div>
                                <div class="row mt16">
                                    <label for="firebase_auth_uri" class="col-lg-3 o_light_label"/>
                                    <field name="firebase_auth_uri" placeholder="https://accounts.google.com/o/oauth2/auth"/>
                                </div>
                                <div class="row mt16">
                                    <label for="firebase_token_uri" class="col-lg-3 o_light_label"/>
                                    <field name="firebase_token_uri" placeholder="https://oauth2.googleapis.com/token"/>
                                </div>
                                <div class="row mt16">
                                    <label for="firebase_auth_provider_cert_url" class="col-lg-3 o_light_label"/>
                                    <field name="firebase_auth_provider_cert_url" placeholder="https://www.googleapis.com/oauth2/v1/certs"/>
                                </div>
                                <div class="row mt16">
                                    <label for="firebase_client_cert_url" class="col-lg-3 o_light_label"/>
                                    <field name="firebase_client_cert_url" placeholder="Client Cert URL"/>
                                </div>
                                <div class="row mt16">
                                    <label for="firebase_universe_domain" class="col-lg-3 o_light_label"/>
                                    <field name="firebase_universe_domain" placeholder="googleapis.com"/>
                                </div>
                            </div>
                        </setting>
                    </block>
                </app>
            </xpath>
        </field>
    </record>
</odoo>
