import logging
import os

import firebase_admin
import requests
from firebase_admin import credentials, messaging
from odoo import _, api, fields, models
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class MobileAppPushNotification(models.Model):
    _name = "mobile.app.push.notification"
    _description = "Push Notification"
    _inherit = ["mail.thread", "mail.activity.mixin"]

    _order = "id desc"

    STATE_SELECTION = [
        ("draft", "Draft"),
        ("done", "Sent"),
        ("Planned", "Planned"),
        ("cancel", "Cancel"),
        ("error", "Error"),
    ]

    name = fields.Char("Title", tracking=True)
    body = fields.Text("Message", tracking=True)
    send_notification_to = fields.Selection(
        [("to_all", "All Partners"), ("to_specefic", "To a partner")],
        string="Send To",
        default="to_all",
        tracking=True,
    )

    log_history = fields.One2many(
        "push.notification.log.history",
        "notification_id",
        "History",
    )
    partner_ids = fields.Many2many("res.partner", string="User")
    state = fields.Selection(
        STATE_SELECTION, "Status", readonly=True, default="draft", tracking=True
    )

    def initialize_firebase(self):
        """Initialize Firebase with credentials from system parameters."""
        # Get credentials from system parameters

        # Check if Firebase is already initialized
        if not firebase_admin._apps:
            # Try to initialize from JSON file first
            try:
                self.initialize_firebase_from_json_file()
                return True
            except Exception as e:
                _logger.warning("Failed to initialize Firebase from JSON file: %s", e)

            # If JSON initialization fails, try from settings
            try:
                self.initialize_firebase_from_settings()
                return True
            except Exception as e:
                _logger.error("Failed to initialize Firebase from settings: %s", e)
                raise UserError(
                    "Failed to initialize Firebase. Please check your configuration."
                )
        return True

    def initialize_firebase_from_settings(self):
        """Initialize Firebase with credentials from system parameters."""
        cred_dict = self.get_firebase_credentials_from_settings()
        cred = credentials.Certificate(cred_dict)
        firebase_admin.initialize_app(cred)
        _logger.info("Firebase initialized from system settings")

    def initialize_firebase_from_json_file(self):
        os_dir = os.path.dirname(__file__)
        module_dir = os.path.abspath(os.path.join(os_dir, ".."))
        json_path = os.path.join(module_dir, "data", "serviceAccountKey.json")

        if os.path.exists(json_path):
            cred = credentials.Certificate(json_path)
            firebase_admin.initialize_app(cred)
            _logger.info("Firebase initialized from JSON file")

    def get_firebase_credentials_from_settings(self):
        """Get Firebase credentials from system parameters instead of the JSON file."""
        ICP = self.env["ir.config_parameter"].sudo()

        return {
            "type": ICP.get_param("firebase_push_notification.type", "service_account"),
            "project_id": ICP.get_param("firebase_push_notification.project_id", ""),
            "private_key_id": ICP.get_param(
                "firebase_push_notification.private_key_id", ""
            ),
            "private_key": ICP.get_param(
                "firebase_push_notification.private_key", ""
            ).replace(r"\n", "\n"),
            "client_email": ICP.get_param(
                "firebase_push_notification.client_email", ""
            ),
            "client_id": ICP.get_param("firebase_push_notification.client_id", ""),
            "auth_uri": ICP.get_param(
                "firebase_push_notification.auth_uri",
                "https://accounts.google.com/o/oauth2/auth",
            ),
            "token_uri": ICP.get_param(
                "firebase_push_notification.token_uri",
                "https://oauth2.googleapis.com/token",
            ),
            "auth_provider_x509_cert_url": ICP.get_param(
                "firebase_push_notification.auth_provider_cert_url",
                "https://www.googleapis.com/oauth2/v1/certs",
            ),
            "client_x509_cert_url": ICP.get_param(
                "firebase_push_notification.client_cert_url", ""
            ),
            "universe_domain": ICP.get_param(
                "firebase_push_notification.universe_domain", "googleapis.com"
            ),
        }

    def send_notification(self):
        self.initialize_firebase()
        tokens = []
        partner_ids = self.env["res.partner"].search([("active", "=", True)])
        if self.send_notification_to == "to_all":
            for rec in partner_ids:
                for line in rec.mail_firebase_tokens.filtered(
                    lambda x: x.active == True
                ):
                    tokens.append(
                        {"partner_id": line.partner_id.id, "token": line.token}
                    )
        else:
            for rec in self.partner_ids:
                for line in rec.mail_firebase_tokens.filtered(
                    lambda x: x.active == True
                ):
                    tokens.append(
                        {"partner_id": line.partner_id.id, "token": line.token}
                    )

        if not tokens:
            raise UserError(
                "No identify tokens for this partner, "
                "please check the token in the partner form"
            )
        message = messaging.MulticastMessage(
            notification=messaging.Notification(
                title=self.name or "",
                body=self.body or "",
            ),
            data=None,
            tokens=[item["token"] for item in tokens],
        )
        response = messaging.send_each_for_multicast(message)
        if response:
            self.env["push.notification.log.history"].sudo().create(
                {
                    "notification_id": self.id,
                    "date_send": fields.Datetime.now(),
                    "notification_state": "success",
                }
            )
            responses = response.responses
            failed_tokens = []
            success_tokens = []
            for idx, resp in enumerate(responses):
                if not resp.success:
                    failed_tokens.append(tokens[idx])
                if resp.success:
                    success_tokens.append(tokens[idx])

            succ_partner_ids = []
            for succ in success_tokens:
                self.env["push.notification.log.partner"].sudo().create(
                    {
                        "notification_id": self.id,
                        "name": self.name,
                        "body": self.body,
                        "partner_id": succ["partner_id"],
                        "date_send": fields.Datetime.now(),
                        "notification_state": "success",
                        "device_token": succ["token"],
                    }
                )
            self.write({"state": "done"})
            for succ in failed_tokens:
                self.env["push.notification.log.partner"].sudo().create(
                    {
                        "notification_id": self.id,
                        "name": self.name,
                        "body": self.body,
                        "partner_id": succ["partner_id"],
                        "date_send": fields.Datetime.now(),
                        "notification_state": "failed",
                        "device_token": succ["token"],
                    }
                )


class PushNotificationLogHistory(models.Model):
    _name = "push.notification.log.history"
    _description = "Push Notification"

    notification_id = fields.Many2one("mobile.app.push.notification")
    date_send = fields.Datetime("Send Date")
    notification_state = fields.Selection(
        [("success", "Success"), ("failed", "Failed")], string="State"
    )
