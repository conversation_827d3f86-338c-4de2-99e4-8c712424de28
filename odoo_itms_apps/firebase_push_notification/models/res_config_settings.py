# -*- coding: utf-8 -*-

from odoo import api, fields, models


class ResConfigSettings(models.TransientModel):
    _inherit = "res.config.settings"

    firebase_type = fields.Char(
        string="Type", config_parameter="firebase_push_notification.type"
    )
    firebase_project_id = fields.Char(
        string="Project ID", config_parameter="firebase_push_notification.project_id"
    )
    firebase_private_key_id = fields.Char(
        string="Private Key ID",
        config_parameter="firebase_push_notification.private_key_id",
    )
    firebase_private_key = fields.Char(
        string="Private Key", config_parameter="firebase_push_notification.private_key"
    )
    firebase_client_email = fields.Char(
        string="Client Email",
        config_parameter="firebase_push_notification.client_email",
    )
    firebase_client_id = fields.Char(
        string="Client ID", config_parameter="firebase_push_notification.client_id"
    )
    firebase_auth_uri = fields.Char(
        string="Auth URI", config_parameter="firebase_push_notification.auth_uri"
    )
    firebase_token_uri = fields.Char(
        string="Token URI", config_parameter="firebase_push_notification.token_uri"
    )
    firebase_auth_provider_cert_url = fields.Char(
        string="Auth Provider Cert URL",
        config_parameter="firebase_push_notification.auth_provider_cert_url",
    )
    firebase_client_cert_url = fields.Char(
        string="Client Cert URL",
        config_parameter="firebase_push_notification.client_cert_url",
    )
    firebase_universe_domain = fields.Char(
        string="Universe Domain",
        config_parameter="firebase_push_notification.universe_domain",
    )
