# models/ir_http.py
from odoo import models
from odoo.http import request


class IrHttp(models.AbstractModel):
    _inherit = "ir.http"

    def session_info(self):
        result = super(IrHttp, self).session_info()
        if request.env.user._is_internal():
            # Add pinned apps to session info
            result["user_pinned_apps"] = request.env.user.pinned_apps.ids
            for company in request.env.user.company_ids.with_context(bin_size=True):
                result["user_companies"]["allowed_companies"][company.id].update(
                    {
                        "has_appsbar_image": bool(company.appbar_image),
                    }
                )
        return result
