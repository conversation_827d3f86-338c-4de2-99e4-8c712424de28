# models/res_users.py
from odoo import api, fields, models


class ResUsers(models.Model):

    _inherit = "res.users"

    # ----------------------------------------------------------
    # Properties
    # ----------------------------------------------------------

    @property
    def SELF_READABLE_FIELDS(self):
        return super().SELF_READABLE_FIELDS + [
            "sidebar_type",
            "pinned_apps",
        ]

    @property
    def SELF_WRITEABLE_FIELDS(self):
        return super().SELF_WRITEABLE_FIELDS + [
            "sidebar_type",
            "pinned_apps",
        ]

    # ----------------------------------------------------------
    # Fields
    # ----------------------------------------------------------

    sidebar_type = fields.Selection(
        selection=[("invisible", "Invisible"),("large", "Large"),("small", "Small")],
        compute="_compute_sidebar_type",
        inverse="_inverse_sidebar_type",
        string="Sidebar Type",
        required=True,
        default="large",
    )

    @property
    def SELF_READABLE_FIELDS(self):
        return super().SELF_READABLE_FIELDS + ['sidebar_type']

    @property
    def SELF_WRITEABLE_FIELDS(self):
        return super().SELF_WRITEABLE_FIELDS + ['sidebar_type']

    @api.depends("res_users_settings_id.sidebar_type")
    def _compute_sidebar_type(self):
        """Compute the sidebar type from user settings."""
        for user in self:
            user.sidebar_type = user.res_users_settings_id.sidebar_type or 'large'

    def _inverse_sidebar_type(self):
        """Update the sidebar type in user settings."""
        for user in self.filtered(lambda user: user._is_internal()):
            settings = self.env["res.users.settings"].sudo()._find_or_create_for_user(user)
            settings.sudo().write({'sidebar_type': user.sidebar_type})

    pinned_apps = fields.Many2many(
        "ir.ui.menu",
        "res_users_pinned_apps_rel",
        "user_id",
        "menu_id",
        string="Pinned Apps",
        domain=[("parent_id", "=", False)],  # Only top-level apps
    )
