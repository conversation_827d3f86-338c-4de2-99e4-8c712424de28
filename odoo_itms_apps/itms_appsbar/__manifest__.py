{
    "name": "ITMS App Bar",
    "summary": "Adds a sidebar to the main screen",
    "description": """
        This module adds a sidebar to the main screen. The sidebar has a list
        of all installed apps similar to the home menu to ease navigation.
    """,
    "version": "********.0",
    "category": "Tools/UI",
    "license": "LGPL-3",
    "author": "ITMS",
    "website": "http://www.itmsgroup.com.au",
    "live_test_url": "https://itmsgroup.com.au",
    "contributors": [
        "<PERSON> <<EMAIL>>",
    ],
    "depends": [
        "base_setup",
        "web",
    ],
    "data": [
        "views/res_users.xml",
        "views/res_config_settings.xml",
    ],
    "assets": {
        "web._assets_primary_variables": [
            "itms_appsbar/static/src/scss/variables.scss",
        ],
        "web._assets_backend_helpers": [
            "itms_appsbar/static/src/scss/mixins.scss",
        ],
        "web.assets_backend": [
            (
                "after",
                "web/static/src/webclient/webclient.js",
                "itms_appsbar/static/src/webclient/webclient.js",
            ),
            (
                "after",
                "web/static/src/webclient/webclient.xml",
                "itms_appsbar/static/src/webclient/webclient.xml",
            ),
            (
                "after",
                "web/static/src/webclient/webclient.js",
                "itms_appsbar/static/src/webclient/menus/app_menu_service.js",
            ),
            (
                "after",
                "web/static/src/webclient/webclient.js",
                "itms_appsbar/static/src/webclient/appsbar/appsbar.js",
            ),
            "itms_appsbar/static/src/webclient/appsbar/appsbar.xml",
            "itms_appsbar/static/src/webclient/appsbar/appsbar.scss",
        ],
        "web.assets_web_dark": [
            ("remove", "itms_appsbar/static/src/scss/variables.scss"),
            (
                "before",
                "itms_appsbar/static/src/webclient/appsbar/appsbar.xml",
                "itms_appsbar/static/src/scss/variables.dark.scss",
            ),
        ],
    },
    "images": [
        "static/description/banner.png",
    ],
    "installable": True,
    "application": True,
    "post_init_hook": "_setup_module",
}
