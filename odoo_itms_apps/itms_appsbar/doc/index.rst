===========
MuK AppsBar
===========

This module adds a sidebar to the main screen. The sidebar has a list
of all installed apps similar to the home menu to ease navigation.

Installation
============

To install this module, you need to:

Download the module and add it to your Odoo addons folder. Afterward, log on to
your Odoo server and go to the Apps menu. Trigger the debug mode and update the
list by clicking on the "Update Apps List" link. Now install the module by
clicking on the install button.

Upgrade
============

To upgrade this module, you need to:

Download the module and add it to your Odoo addons folder. Restart the server
and log on to your Odoo server. Select the Apps menu and upgrade the module by
clicking on the upgrade button.

Configuration
=============

Each user can define in their profile the display of the sidebar. The options
are to show the large version, the small version, or hide the sidebar completely.
In addition, an image can be added for each company, which is displayed at the
bottom of the sidebar.

Usage
=============

Is the sidebar is activated it will be automatically displayed on the left side of
the systems UI.
