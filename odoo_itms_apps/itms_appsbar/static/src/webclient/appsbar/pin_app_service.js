import { registry } from "@web/core/registry";
import { browser } from "@web/core/browser/browser";

export const pinAppService = {
    dependencies: ["rpc"],
    async start(env, { rpc }) {
        let pinnedApps = browser.localStorage.getItem('pinned_apps');
        pinnedApps = pinnedApps ? JSON.parse(pinnedApps) : [];

        return {
            async togglePinnedApp(appId) {
                const result = await rpc('/web/dataset/call_kw/res.users/write', {
                    model: 'res.users',
                    method: 'write',
                    args: [[env.services.user.userId], {
                        pinned_apps: [[3, appId]], // Toggle the many2many field
                    }],
                    kwargs: {},
                });
                return result;
            },
            isPinned(appId) {
                return env.services.user.pinnedApps.includes(appId);
            },
        };
    },
};

registry.category("services").add("pin_app", pinAppService);
