// Variables

$mk-sidebar-large-width: 180px;
$mk-sidebar-small-width: 46px;

// Mixin for custom scrollbar
@mixin mk-disable-scrollbar() {
    &::-webkit-scrollbar {
        width: 0px;
        height: 0px;
    }
    &::-webkit-scrollbar-thumb {
        background: transparent;
    }
}

.o_web_client {
    display: grid !important;
    grid-template-areas:
        "banner banner"
        "navbar navbar"
        "sidebar content"
        "components components";
    grid-template-rows: auto auto 1fr auto;
    grid-template-columns: auto 1fr;
    > div:has(#oe_neutralize_banner) {
        grid-area: banner;
    }
    > .o_navbar {
        grid-area: navbar;
    }
    > .mk_apps_sidebar_panel {
        grid-area: sidebar;
    }
    > .o_action_manager {
        grid-area: content;
    }
    > .o-main-components-container {
        grid-area: components;
    }
}

.mk_apps_sidebar_panel {
    @include mk-disable-scrollbar();
    background-color: $mk-appbar-background;
    width: var(--mk-sidebar-width, 0);
    overflow-y: auto;
    transition: width 0.3s ease;

    .mk_apps_sidebar {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        white-space: nowrap;

        .mk_apps_sidebar_header {
            padding: 4px;
            border-bottom: 1px solid $mk-appbar-border-color;
            display: flex;
            align-items: center;
            gap: 2px;

            .mk_sidebar_toggle {
                color: $mk-appbar-color;
                background: none;
                border: none;
                padding: 1px;
                cursor: pointer;
                opacity: 0.7;
                font-size: 12px;

                &:hover {
                    opacity: 1;
                }
            }

            .mk_apps_search {
                flex: 1;
                input {
                    width: 100%;
                    background: $mk-appbar-search-background;
                        border: 1px solid $mk-appbar-search-border-color;
                    border-radius: 3px;
                    color: $mk-appbar-color;
                    padding: 1px 4px;
                    font-size: 12px;
                    height: 22px;

                    &::placeholder {
                        color: $mk-appbar-placeholder-color;
                    }
                }
            }
        }

        .mk_apps_sidebar_menu {
            padding: 0;
            margin: 0;
            list-style: none;

            .favorites-header {
                padding: 4px 6px 2px;
                font-size: 11px;
                color: $mk-appbar-color;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            > li > a {
                cursor: pointer;
                font-size: 12px;
                font-weight: normal;
                overflow: hidden;
                padding: 4px 6px;
                text-decoration: none;
                color: $mk-appbar-color;
                text-overflow: ellipsis;
                display: flex;
                align-items: center;
                position: relative;
                line-height: 1;

                .mk_apps_sidebar_icon {
                    width: 16px;
                    height: 16px;
                    margin-right: 4px;
                }

                .mk_apps_sidebar_name {
                    flex: 1;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .mk_pin_button {
                    width: 24px;
                    height: 24px;
                    padding: 4px;
                    background: none;
                    border: none;
                    border-radius: 3px;
                    cursor: pointer;
                    opacity: 0.6;
                    transition: all 0.2s ease;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    svg {
                        width: 14px;
                        height: 14px;
                        fill: currentColor;
                        transition: transform 0.2s ease;
                    }

                    &:hover {
                        opacity: 1;
                        background: $mk-appbar-border-color;
                    }

                    &.pinned {
                        opacity: 1;
                        svg {
                            fill: $mk-appbar-pin-color;
                            transform: rotate(-45deg);
                        }
                    }
                }
            }

            > li:hover > a {
                background: $mk-appbar-border-color;

                .mk_pin_button {
                    opacity: 1;
                }
            }

            > li.active > a {
                background: $mk-appbar-active;
            }
        }

        .mk_pinned_apps {
            border-bottom: 1px solid $mk-appbar-border-color;
            margin-bottom: 4px;
            padding-bottom: 2px;
        }

                // Company logo styles (if needed in future)
                // .mk_apps_sidebar_logo {
                //     margin-top: auto;
                //     padding: 6px;
                //     img {
                //         max-width: 100%;
                //         height: auto;
                //     }
                // }
    }
}

.mk_sidebar_type_large {
    --mk-sidebar-width: #{$mk-sidebar-large-width};
}

.mk_sidebar_type_small {
    --mk-sidebar-width: #{$mk-sidebar-small-width};

    .mk_apps_sidebar_name,
    .mk_apps_search,
    .favorites-header span,
    .mk_pin_button {
        display: none !important;
    }

    .mk_apps_sidebar_icon {
        margin-right: 0 !important;
    }

    .mk_apps_sidebar_menu > li > a {
        justify-content: center;
        padding: 4px 2px;
    }
}

.mk_sidebar_type_invisible {
    --mk-sidebar-width: 0;
}

@include media-breakpoint-only(md) {
    .mk_sidebar_type_large {
        --mk-sidebar-width: #{$mk-sidebar-small-width};

        .mk_apps_sidebar_name,
        .mk_apps_search,
        .favorites-header span,
        .mk_pin_button {
            display: none !important;
        }

        .mk_apps_sidebar_icon {
            margin-right: 0 !important;
        }

        .mk_apps_sidebar_menu > li > a {
            justify-content: center;
            padding: 4px 2px;
        }
    }
}

@include media-breakpoint-down(md) {
    .mk_sidebar_type_large,
    .mk_sidebar_type_small {
        --mk-sidebar-width: 0;
    }
}
