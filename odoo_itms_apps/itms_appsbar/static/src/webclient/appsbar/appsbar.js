import { useService } from '@web/core/utils/hooks';
import { user } from "@web/core/user";
import { rpc } from "@web/core/network/rpc";
import { Component, onWillUnmount, useState } from '@odoo/owl';

export class AppsBar extends Component {
    static template = 'itms_appsbar.AppsBar';

    setup() {
        this.appMenuService = useService('app_menu');
        this.userService = user;

        const userSidebarType = this.userService.settings?.sidebar_type || 'large';

        let initialSidebarState = true; // default to expanded

        if (userSidebarType === 'invisible') {
            initialSidebarState = false;
        } else if (userSidebarType === 'small') {
            // For small mode, check localStorage for user's last preference
            const storedSidebarState = localStorage.getItem('odoo_appsbar_expanded');
            initialSidebarState = storedSidebarState !== null
                ? JSON.parse(storedSidebarState)
                : false; // default to collapsed for small mode
        }

        this.state = useState({
            searchQuery: '',
            isExpanded: initialSidebarState,
            sidebarType: userSidebarType
        });

        // Initialize company logo (if needed in future)
        // if (this.companyService.currentCompany.has_appsbar_image) {
        //     this.sidebarImageUrl = url('/web/image', {
        //         model: 'res.company',
        //         field: 'appbar_image',
        //         id: this.companyService.currentCompany.id,
        //     });
        // }

        this.pinnedAppIds = [];
        this.loadPinnedApps();

        const renderAfterMenuChange = () => this.render();
        this.env.bus.addEventListener('MENUS:APP-CHANGED', renderAfterMenuChange);

        const handleSessionRefresh = () => this.refreshFromSession();
        this.env.bus.addEventListener('SESSION_REFRESH', handleSessionRefresh);

        onWillUnmount(() => {
            this.env.bus.removeEventListener('MENUS:APP-CHANGED', renderAfterMenuChange);
            this.env.bus.removeEventListener('SESSION_REFRESH', handleSessionRefresh);
        });

        this.updateSidebarWidth();
    }

    async loadPinnedApps() {
        try {
            const result = await rpc('/web/dataset/call_kw/res.users/read', {
                model: 'res.users',
                method: 'read',
                args: [[user.userId], ['pinned_apps']],
                kwargs: {},
            });
            this.pinnedAppIds = result[0].pinned_apps || [];
            this.render();
        } catch (error) {
            console.error('Error loading pinned apps:', error);
        }
    }

    async refreshFromSession() {
        try {
            const sessionInfo = await rpc('/web/session/get_session_info');
            const newSidebarType = sessionInfo.user_settings?.sidebar_type || 'large';
            if (newSidebarType !== this.state.sidebarType) {
                this.updateSidebarType(newSidebarType);
            }
            if (sessionInfo.user_pinned_apps) {
                this.pinnedAppIds = sessionInfo.user_pinned_apps;
            }
            this.render();
        } catch (error) {
            console.error('Error refreshing from session:', error);
        }
    }

    get pinnedApps() {
        const allApps = this.appMenuService.getAppsMenuItems();
        return this.pinnedAppIds
            .map(id => allApps.find(app => app.id === id))
            .filter(Boolean);
    }

    get regularApps() {
        const allApps = this.appMenuService.getAppsMenuItems();
        const query = this.state.searchQuery.toLowerCase();
        const apps = allApps.filter(app => !this.pinnedAppIds.includes(app.id));

        if (!query) {
            return apps;
        }

        return apps.filter(app => app.name.toLowerCase().includes(query));
    }

    async togglePin(appId) {
        try {
            const action = this.pinnedAppIds.includes(appId) ? 3 : 4; // 3 for remove, 4 for add
            await rpc('/web/dataset/call_kw/res.users/write', {
                model: 'res.users',
                method: 'write',
                args: [[user.userId], {
                    pinned_apps: [[action, appId]],
                }],
                kwargs: {},
            });
            await this.loadPinnedApps();
        } catch (error) {
            console.error('Error toggling pin:', error);
        }
    }

    toggleSidebar() {
        this.state.isExpanded = !this.state.isExpanded;
        // Save the sidebar state to local storage
        localStorage.setItem('odoo_appsbar_expanded', JSON.stringify(this.state.isExpanded));
        this.updateSidebarWidth();
    }

    updateSidebarType(newSidebarType) {
        this.state.sidebarType = newSidebarType;

        if (newSidebarType === 'invisible') {
            this.state.isExpanded = false;
        } else if (newSidebarType === 'small') {
            const storedSidebarState = localStorage.getItem('odoo_appsbar_expanded');
            this.state.isExpanded = storedSidebarState !== null
                ? JSON.parse(storedSidebarState)
                : false;
        } else if (newSidebarType === 'large') {
            const storedSidebarState = localStorage.getItem('odoo_appsbar_expanded');
            this.state.isExpanded = storedSidebarState !== null
                ? JSON.parse(storedSidebarState)
                : true;
        }

        this.updateSidebarWidth();
    }

    get isSidebarVisible() {
        return this.state.sidebarType !== 'invisible';
    }

    updateSidebarWidth() {
        const sidebarPanel = document.querySelector('.mk_apps_sidebar_panel');
        if (sidebarPanel) {
            if (this.state.sidebarType === 'invisible') {
                sidebarPanel.style.setProperty('--mk-sidebar-width', '0px');
            } else if (this.state.isExpanded) {
                sidebarPanel.style.setProperty('--mk-sidebar-width', '180px');
            } else {
                sidebarPanel.style.setProperty('--mk-sidebar-width', '46px');
            }
        }
    }

    onSearchInput(ev) {
        this.state.searchQuery = ev.target.value || '';
    }

    onAppClick(app, ev) {
        ev.preventDefault();
        app.action();
    }
}
