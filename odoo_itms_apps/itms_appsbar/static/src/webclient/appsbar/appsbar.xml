<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="itms_appsbar.AppsBar">
        <div t-if="isSidebarVisible" class="mk_apps_sidebar_panel" t-att-class="{'mk_sidebar_type_large': state.isExpanded, 'mk_sidebar_type_small': !state.isExpanded}">
            <div class="mk_apps_sidebar">
                <!-- Search and Toggle -->
                <div class="mk_apps_sidebar_header p-2">
                    <button class="mk_sidebar_toggle btn btn-link p-0 border-0" t-on-click="toggleSidebar">
                        <i t-attf-class="fa fa-chevron-{{state.isExpanded ? 'left' : 'right'}}"/>
                    </button>
                    <div t-if="state.isExpanded" class="mk_apps_search ms-2">
                        <input
                            type="text"
                            class="form-control form-control-sm"
                            placeholder="Search apps..."
                            t-on-input="onSearchInput"
                            t-model="state.searchQuery"
                        />
                    </div>
                </div>

                <!-- Pinned Apps -->
                <t t-if="pinnedApps.length">
                    <ul class="mk_apps_sidebar_menu mk_pinned_apps">
                        <li class="nav-item favorites-header">
                            <span class="nav-link">
                                <i class="fa fa-star text-warning me-2"/> Favorites
                            </span>
                        </li>
                        <t t-foreach="pinnedApps" t-as="app" t-key="app.id">
                            <li t-attf-class="nav-item {{ app.id === appMenuService.getCurrentApp()?.id ? 'active' : '' }}">
                                <a t-att-href="app.href"
                                   class="nav-link d-flex align-items-center"
                                   t-on-click="(ev) => onAppClick(app, ev)">
                                    <img t-if="app.webIconData"
                                         class="mk_apps_sidebar_icon"
                                         t-att-src="app.webIconData"/>
                                    <img t-else=""
                                         class="mk_apps_sidebar_icon"
                                         src="/base/static/description/icon.png"/>
                                    <span t-if="state.isExpanded" class="mk_apps_sidebar_name flex-grow-1">
                                        <t t-esc="app.name"/>
                                    </span>
                                    <button t-if="state.isExpanded"
                                            t-attf-class="mk_pin_button {{pinnedAppIds.includes(app.id) ? 'pinned' : ''}}"
                                            t-on-click.stop="() => this.togglePin(app.id)">
                                        <svg viewBox="0 0 24 24" width="16" height="16" t-att-title="pinnedAppIds.includes(app.id) ? 'Unpin from favorites' : 'Pin to favorites'">
                                            <path d="M14 4v5c0 1.12.37 2.16 1 3H9c.65-.84 1-1.88 1-3V4h4M17 2H7c-.55 0-1 .45-1 1s.45 1 1 1h1v5c0 1.66-1.34 3-3 3v2h5.97v7l1 1 1-1v-7H19v-2c-1.66 0-3-1.34-3-3V4h1c.55 0 1-.45 1-1s-.45-1-1-1z"/>
                                        </svg>
                                    </button>
                                </a>
                            </li>
                        </t>
                    </ul>
                    <div class="mk_apps_sidebar_separator"/>
                </t>

                <!-- Regular Apps -->
                <ul class="mk_apps_sidebar_menu">
                    <t t-foreach="regularApps" t-as="app" t-key="app.id">
                        <li t-attf-class="nav-item {{ app.id === appMenuService.getCurrentApp()?.id ? 'active' : '' }}">
                            <a t-att-href="app.href"
                               class="nav-link d-flex align-items-center"
                               t-on-click="(ev) => onAppClick(app, ev)">
                                <img t-if="app.webIconData"
                                     class="mk_apps_sidebar_icon"
                                     t-att-src="app.webIconData"/>
                                <img t-else=""
                                     class="mk_apps_sidebar_icon"
                                     src="/base/static/description/icon.png"/>
                                <span t-if="state.isExpanded" class="mk_apps_sidebar_name flex-grow-1">
                                    <t t-esc="app.name"/>
                                </span>
                                <button t-if="state.isExpanded"
                                        t-attf-class="mk_pin_button {{pinnedAppIds.includes(app.id) ? 'pinned' : ''}}"
                                        t-on-click.stop="() => this.togglePin(app.id)">
                                    <svg viewBox="0 0 24 24" width="16" height="16" t-att-title="pinnedAppIds.includes(app.id) ? 'Unpin from favorites' : 'Pin to favorites'">
                                        <path d="M14 4v5c0 1.12.37 2.16 1 3H9c.65-.84 1-1.88 1-3V4h4M17 2H7c-.55 0-1 .45-1 1s.45 1 1 1h1v5c0 1.66-1.34 3-3 3v2h5.97v7l1 1 1-1v-7H19v-2c-1.66 0-3-1.34-3-3V4h1c.55 0 1-.45 1-1s-.45-1-1-1z"/>
                                    </svg>
                                </button>
                            </a>
                        </li>
                    </t>
                </ul>
            </div>
        </div>
    </t>
</templates>
